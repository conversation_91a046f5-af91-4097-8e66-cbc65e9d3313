[0/1] Re-running CMake...
-- ccache will be used for faster recompilation
-- Building ESP-IDF components for target esp32s3
NOTICE: Using component placed at C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils for dependency "nimble_peripheral_utils", specified in D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\main\idf_component.yml
NOTICE: Processing 3 dependencies:
NOTICE: [1/3] joltwallet/littlefs (1.14.8)
NOTICE: [2/3] nimble_peripheral_utils (*) (C:\Users\<USER>\esp\v5.4.1\esp-idf\examples\bluetooth\nimble\common\nimble_peripheral_utils)
NOTICE: [3/3] idf (5.4.1)
-- Project sdkconfig file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig
Loading defaults file D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults...
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BLE_ONLY' assigned to 'y' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BR_EDR_ONLY' assigned to 'n' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
warning: unknown kconfig symbol 'BTDM_CTRL_MODE_BTDM' assigned to 'n' in D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- App "Vantage_nxESP32_Int_Rel_1_2_1_0" version: Vantage_Bin_nxESP32_1_2_0_0
-- Adding linker script D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 joltwallet__littlefs json log lwip main mbedtls mqtt newlib nimble_peripheral_utils nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/managed_components/joltwallet__littlefs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib C:/Users/<USER>/esp/v5.4.1/esp-idf/examples/bluetooth/nimble/common/nimble_peripheral_utils C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport C:/Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (43.8s)
-- Generating done (2.5s)
-- Build files have been written to: D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build
[1/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/crc.c.obj
[2/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/fw_file_handler.c.obj
[3/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_request_handler.c.obj
[4/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/flash_handler.c.obj
[5/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr_handler.c.obj
[6/27] Generating ../../partition_table/partition-table.bin
Partition table binary generated. Contents:
*******************************************************************************
# ESP-IDF Partition Table

# Name, Type, SubType, Offset, Size, Flags

nvs,data,nvs,0x9000,16K,

otadata,data,ota,0xd000,8K,

phy_init,data,phy,0xf000,4K,

factory,app,factory,0x10000,1M,

ota_0,app,ota_0,0x110000,1M,

ota_1,app,ota_1,0x210000,1M,

nexus_board,data,esphttpd,0x310000,12M,

*******************************************************************************
[7/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/gatt_svr.c.obj
[8/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/led_control.c.obj
[9/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ota_handler.c.obj
[10/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/timer_handler.c.obj
[11/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/utility.c.obj
[12/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_app.c.obj
[13/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_ota_service.c.obj
[14/27] Generating ../../ota_data_initial.bin
[15/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_response_handler.c.obj
[16/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj 
ccache C:\Users\<USER>\.espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin\xtensa-esp32s3-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"v5.4.1\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/config -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Inc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/include/freertos -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/config/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/esp_additions/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/dma/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/ldo/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/debug_probe/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/tlsf -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/register -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/include/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/include/private -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/deprecated_include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include/apps -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/include/apps/sntp -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/freertos/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include/arch -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/include/sys -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/i2c/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/twai/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/src -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/vfs -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/include/esp32c3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/osi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/api/include/api -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/btc/profile/esp/blufi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/btc/profile/esp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/hci_log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/common/ble_log/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ans/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/bas/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/dis/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/gap/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/gatt/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/hr/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/htp/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ias/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/ipss/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/lls/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/prox/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/cts/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/tps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/hid/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/sps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/services/cte/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/util/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/store/ram/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/host/store/config/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/porting/nimble/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/port/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/nimble/transport/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/porting/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/nimble/porting/npl/freertos/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/host/nimble/esp-hci/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/include/local -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/wifi_apps/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/wifi_apps/nan_app/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/esp32s3/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/include -ID:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/managed_components/joltwallet__littlefs/include -IC:/Users/<USER>/esp/v5.4.1/esp-idf/examples/bluetooth/nimble/common/nimble_peripheral_utils -mlongcalls  -fno-builtin-memcpy -fno-builtin-memset -fno-builtin-bzero -fno-builtin-stpcpy -fno-builtin-strncpy -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-error=extra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -mdisable-hardware-atomics -Og -fno-shrink-wrap -fmacro-prefix-map=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.4.1/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\Src\main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj -c D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c
In file included from D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c:22:
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c: In function 'app_main':
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c:123:26: error: 'tag' undeclared (first use in this function)
  123 |                 ESP_LOGE(tag, "Failed to initialize OTA manager: %s", esp_err_to_name(ota_err));
      |                          ^~~
C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include/esp_log.h:182:81: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                 ^~~
C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include/esp_log.h:112:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  112 | #define ESP_LOGE( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_ERROR,   tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c:123:17: note: in expansion of macro 'ESP_LOGE'
  123 |                 ESP_LOGE(tag, "Failed to initialize OTA manager: %s", esp_err_to_name(ota_err));
      |                 ^~~~~~~~
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c:123:26: note: each undeclared identifier is reported only once for each function it appears in
  123 |                 ESP_LOGE(tag, "Failed to initialize OTA manager: %s", esp_err_to_name(ota_err));
      |                          ^~~
C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include/esp_log.h:182:81: note: in definition of macro 'ESP_LOG_LEVEL'
  182 |         if (level==ESP_LOG_ERROR )          { esp_log_write(ESP_LOG_ERROR,      tag, LOG_FORMAT(E, format), esp_log_timestamp(), tag, ##__VA_ARGS__); } \
      |                                                                                 ^~~
C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/include/esp_log.h:112:38: note: in expansion of macro 'ESP_LOG_LEVEL_LOCAL'
  112 | #define ESP_LOGE( tag, format, ... ) ESP_LOG_LEVEL_LOCAL(ESP_LOG_ERROR,   tag, format, ##__VA_ARGS__)
      |                                      ^~~~~~~~~~~~~~~~~~~
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/main.c:123:17: note: in expansion of macro 'ESP_LOGE'
  123 |                 ESP_LOGE(tag, "Failed to initialize OTA manager: %s", esp_err_to_name(ota_err));
      |                 ^~~~~~~~
[17/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ota_manager.c.obj
[18/27] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x51e0 bytes. 0x2e20 bytes (36%) free.


[19/27] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/uart_handler.c.obj
ninja: build stopped: subcommand failed.
