# BLE Bonding Usage Example

## Overview

This document provides practical examples of how to use the BLE bonding functionality. The key concept is:

**UART Commands → ESP32 BLE Configuration → BLE Client Bonding Behavior**

## Scenario 1: Mobile App Controlling BLE Bonding

### Setup
- ESP32 with BLE bonding implementation
- Mobile app connected via UART (or PC with Python script)
- BLE client device (phone, tablet, etc.) that wants to connect via BLE

### Workflow

```
Mobile App → UART → ESP32 → BLE Stack → BLE Client
    |                         |           |
    |                         |           └─ Can pair and bond
    |                         |
    |                         └─ Configures bonding behavior
    |
    └─ Sends bonding control commands
```

### Step-by-Step Example

1. **Enable BLE Bonding via UART**
   ```python
   # Mobile app or PC sends UART command
   python test_bonding.py --command enable
   ```
   
   **Result**: ESP32 BLE stack is configured to allow bonding

2. **BLE Client Connects**
   ```
   BLE Client → ESP32 (BLE connection)
   ```
   
   **Result**: BLE client can now pair and bond with ESP32

3. **Check Bonding Status via UART**
   ```python
   # Check how many BLE devices are bonded
   python test_bonding.py --command status
   ```
   
   **Result**: Shows bonding enabled and count of bonded BLE devices

4. **Clear BLE Bonding Data via UART**
   ```python
   # Remove all BLE pairing information
   python test_bonding.py --command clear
   ```
   
   **Result**: All BLE bonding data is cleared from ESP32

## Scenario 2: Factory Reset via UART

### Use Case
You want to clear all BLE pairing data when a user performs a factory reset.

### Implementation
```python
# Send UART command to clear all BLE bonding data
python test_bonding.py --command clear
```

**What happens:**
1. UART command received by ESP32
2. ESP32 clears all BLE bonding information
3. ESP32 disables BLE bonding
4. All previously paired BLE devices must re-pair

## Scenario 3: Dynamic Bonding Control

### Use Case
Allow users to enable/disable BLE bonding through your mobile app.

### Mobile App Flow
```
User Setting: "Allow BLE Device Pairing"
    ↓
[ON]  → Send BLE_BOND_ENABLE via UART  → ESP32 allows BLE bonding
[OFF] → Send BLE_BOND_DISABLE via UART → ESP32 prevents BLE bonding
```

### Code Example
```python
def set_ble_bonding(enable: bool):
    if enable:
        # Enable BLE bonding
        result = send_uart_command(BLE_BOND_ENABLE)
        if result:
            print("BLE bonding enabled - devices can pair")
    else:
        # Disable BLE bonding  
        result = send_uart_command(BLE_BOND_DISABLE)
        if result:
            print("BLE bonding disabled - connections are temporary")
```

## Scenario 4: Security Management

### Use Case
Monitor and manage BLE security for your device.

### Implementation
```python
# Check current BLE bonding status
status = get_ble_bonding_status()
print(f"BLE Bonding: {status['enabled']}")
print(f"Bonded BLE Devices: {status['count']}")

# If too many devices are bonded, clear some
if status['count'] > 5:
    clear_ble_bonding_data()
    print("Cleared BLE bonding data - too many devices")
```

## Important Notes

### What These Commands DO:
✅ **Control BLE bonding behavior** on the ESP32  
✅ **Configure BLE security settings** for BLE clients  
✅ **Manage BLE pairing data** storage  
✅ **Enable/disable BLE device bonding**  

### What These Commands DON'T Do:
❌ **Create UART bonding** (UART is just the control interface)  
❌ **Affect UART communication** security  
❌ **Bond with UART devices** (UART is the command channel)  

### Communication Layers:
```
Layer 1: UART Communication (Control Channel)
    Mobile App ←→ ESP32
    
Layer 2: BLE Communication (Data Channel)  
    BLE Client ←→ ESP32
    
The UART commands control Layer 2 (BLE) behavior!
```

## Testing Examples

### Complete Test Workflow
```bash
# 1. Check initial status
python test_bonding.py --command status

# 2. Enable BLE bonding
python test_bonding.py --command enable

# 3. Now BLE clients can pair with ESP32
# (Use your phone or BLE scanner to connect)

# 4. Check status again (should show bonded devices)
python test_bonding.py --command status

# 5. Clear all BLE bonding data
python test_bonding.py --command clear

# 6. Disable BLE bonding
python test_bonding.py --command disable
```

### Interactive Testing
```bash
# Start interactive mode
python test_bonding.py --command interactive

# Follow the menu:
# 1 - Enable BLE bonding
# 2 - Disable BLE bonding  
# 3 - Clear all BLE bonding info
# 4 - Get BLE bonding status
# q - Quit
```

## Integration with Your Application

### In Your Mobile App
```javascript
// Enable BLE bonding when user wants to pair devices
function enableBLEPairing() {
    sendUARTCommand(BLE_BOND_ENABLE);
    showMessage("BLE pairing enabled - you can now pair devices");
}

// Disable BLE bonding for security
function disableBLEPairing() {
    sendUARTCommand(BLE_BOND_DISABLE);
    showMessage("BLE pairing disabled - connections are temporary");
}

// Factory reset BLE data
function factoryResetBLE() {
    sendUARTCommand(BLE_BOND_CLEAR);
    showMessage("All BLE pairing data cleared");
}
```

### In Your ESP32 Firmware
```c
// The bonding commands are automatically handled in uart_handler.c
// You can also call the functions directly:

void user_enable_ble_bonding() {
    if (enable_ble_bonding() == 0) {
        ESP_LOGI("APP", "BLE bonding enabled for clients");
    }
}

void user_factory_reset() {
    if (clear_all_bonding_info() == 0) {
        ESP_LOGI("APP", "All BLE bonding data cleared");
    }
}
```

This implementation gives you complete control over BLE bonding behavior through simple UART commands, while keeping the BLE and UART communication layers properly separated.
