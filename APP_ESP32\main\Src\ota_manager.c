/*******************************************************************************
* @file           : ota_manager.c
* @brief          : OTA (Over-The-Air) update manager for ESP32
* @version        : 1.1.0
* @date           : July 20, 2025
* <AUTHOR> Vantage Elevation
* @copyright      : Copyright (C) 2023-2025 by Vantage Elevation
*
* @description    : This file implements OTA functionality using standard ESP-IDF
*                   APIs. It handles firmware updates, validation, and rollback
*                   protection with enhanced bootloader support.
*
* @features       : - Standard ESP-IDF OTA implementation
*                   - Firmware validation and integrity checks
*                   - Automatic rollback protection
*                   - Progress tracking and logging
*                   - Enhanced bootloader compatibility
*****************************************************************************/

#include "esp_ota_ops.h"
#include "esp_log.h"
#include "esp_partition.h"
#include "esp_system.h"
#include "esp_app_desc.h"
#include "esp_image_format.h"

#include "ota_manager.h"
#include "ble_ota_service.h"

static const char *TAG = "ota";

/*******************************************************************************
 * OTA MANAGER VARIABLES
 ******************************************************************************/

/* Standard ESP-IDF OTA variables */
static esp_ota_handle_t ota_handle = 0;
static const esp_partition_t *ota_partition = NULL;
static const esp_partition_t *running_partition = NULL;
static size_t binary_file_length = 0;
static bool image_header_was_checked = false;
static bool ota_in_progress = false;

/* Progress tracking variables */
static uint32_t total_chunks = 0;
static uint32_t received_chunks = 0;
static uint32_t expected_firmware_size = 0;
static uint32_t milestone_chunks[10]; // Pre-calculated milestone chunk numbers
static uint8_t next_milestone_index = 0;



/**
 * @brief Initialize OTA manager (Standard ESP-IDF approach)
 */
esp_err_t ota_manager_init(void)
{
    ESP_LOGI(TAG, "Starting OTA manager initialization");
    
    /* Get running partition info */
    running_partition = esp_ota_get_running_partition();
    if (running_partition == NULL) {
        ESP_LOGE(TAG, "Failed to get running partition");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Running from partition: %s", running_partition->label);

    /* Check if this is first boot after OTA update (rollback validation) */
    esp_ota_img_states_t ota_state;
    if (esp_ota_get_state_partition(running_partition, &ota_state) == ESP_OK) {
        if (ota_state == ESP_OTA_IMG_PENDING_VERIFY) {
            ESP_LOGI(TAG, "First boot after OTA update - validating new firmware...");

            /* Mark the new firmware as valid (prevents rollback) */
            esp_err_t err = esp_ota_mark_app_valid_cancel_rollback();
            if (err == ESP_OK) {
                ESP_LOGI(TAG, "New firmware validated successfully - rollback cancelled");
            } else {
                ESP_LOGE(TAG, "Failed to validate new firmware: %s", esp_err_to_name(err));
            }
        } else if (ota_state == ESP_OTA_IMG_VALID) {
            ESP_LOGI(TAG, "Running validated firmware");
        } else if (ota_state == ESP_OTA_IMG_INVALID) {
            ESP_LOGW(TAG, "Running firmware marked as invalid");
        }
    }

    /* Get next update partition */
    ota_partition = esp_ota_get_next_update_partition(NULL);
    if (ota_partition == NULL) {
        ESP_LOGE(TAG, "Failed to get next update partition");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Next update partition: %s", ota_partition->label);
    
    /* Log current app description */
    const esp_app_desc_t *app_desc = esp_app_get_description();
    ESP_LOGI(TAG, "Current firmware version: %s", app_desc->version);
    ESP_LOGI(TAG, "Compile time: %s %s", app_desc->date, app_desc->time);
    
    ESP_LOGI(TAG, "OTA manager initialized successfully");

    return ESP_OK;
}

/**
 * @brief Start OTA process (Standard ESP-IDF esp_ota_begin)
 */
esp_err_t ota_manager_start(void)
{
    if (ota_in_progress) {
        ESP_LOGW(TAG, "OTA already in progress, aborting previous session...");
        esp_ota_abort(ota_handle);
        ota_in_progress = false;
        ESP_LOGI(TAG, "Previous OTA session aborted, starting new session");
    }

    ESP_LOGI(TAG, "Starting OTA update");
    
    /* Begin OTA */
    esp_err_t err = esp_ota_begin(ota_partition, OTA_SIZE_UNKNOWN, &ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_begin failed: %s", esp_err_to_name(err));
        return err;
    }
    
    ota_in_progress = true;
    binary_file_length = 0;
    image_header_was_checked = false;

    /* Reset progress tracking */
    received_chunks = 0;
    next_milestone_index = 0;
    if (expected_firmware_size > 0) {
        total_chunks = (expected_firmware_size + 503) / 504; // 504 bytes per chunk (512 - 8 byte header)

        // Pre-calculate milestone chunks to match Python client exactly
        // Python logic: shows 10% when (chunks/total)*100 >= 10
        // So we need to find the first chunk where this is true
        for (int i = 0; i < 10; i++) {
            // Find first chunk where (chunk/total)*100 >= (i+1)*10
            // Rearranging: chunk >= (i+1)*10*total/100
            milestone_chunks[i] = ((i + 1) * 10 * total_chunks + 99) / 100; // +99 for ceiling
        }
        ESP_LOGI(TAG, "Expected %lu chunks for %lu byte firmware", total_chunks, expected_firmware_size);
    }
    
    ESP_LOGI(TAG, "OTA started successfully");
    ble_ota_send_status(OTA_STATUS_ACK, OTA_ERROR_NONE, 0);
    
    return ESP_OK;
}

/**
 * @brief Process firmware chunk (Standard ESP-IDF esp_ota_write)
 * This function matches your existing BLE OTA service interface
 */
esp_err_t ota_manager_process_chunk(const uint8_t *data, uint16_t length)
{
    if (!ota_in_progress) {
        ESP_LOGE(TAG, "OTA not in progress");
        return ESP_ERR_INVALID_STATE;
    }

    /* Validate length */
    if (length == 0 || length > OTA_MAX_CHUNK_SIZE) {
        ESP_LOGE(TAG, "Invalid chunk size: %d", length);
        return ESP_ERR_INVALID_ARG;
    }

    /* Check image header on first chunk */
    if (!image_header_was_checked) {
        esp_app_desc_t new_app_info;
        if (length > sizeof(esp_image_header_t) + sizeof(esp_image_segment_header_t) + sizeof(esp_app_desc_t)) {
            memcpy(&new_app_info, &data[sizeof(esp_image_header_t) + sizeof(esp_image_segment_header_t)], sizeof(esp_app_desc_t));
            ESP_LOGI(TAG, "New firmware version: %s", new_app_info.version);

            const esp_app_desc_t *running_app_info = esp_app_get_description();
            if (memcmp(new_app_info.version, running_app_info->version, sizeof(new_app_info.version)) == 0) {
                ESP_LOGW(TAG, "New firmware version is the same as current version");
                ESP_LOGW(TAG, "Current: %s, New: %s", running_app_info->version, new_app_info.version);
                ESP_LOGW(TAG, "Skipping OTA update - no version change detected");
                return ESP_FAIL;
            }
            image_header_was_checked = true;
        } else {
            ESP_LOGE(TAG, "Received package is not fit len");
            return ESP_ERR_INVALID_ARG;
        }
    }

    /* Write data to OTA partition */
    esp_err_t err = esp_ota_write(ota_handle, (const void *)data, length);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_write failed: %s", esp_err_to_name(err));
        return err;
    }

    binary_file_length += length;
    received_chunks++;

    /* Show progress - FAST version with pre-calculated milestones */
    if (total_chunks > 0 && next_milestone_index < 10) {
        // Check if we've reached the exact milestone chunk number
        if (received_chunks == milestone_chunks[next_milestone_index]) {
            ESP_LOGI(TAG, "Progress: %d0%% (%lu/%lu chunks)",
                     next_milestone_index + 1, received_chunks, total_chunks);
            next_milestone_index++;
        }
    } else if (total_chunks == 0) {
        /* Fallback: show progress every 64KB if total size unknown */
        static uint32_t last_progress_kb = 0;
        uint32_t current_kb = binary_file_length / 1024;
        if (current_kb != last_progress_kb && (current_kb % 64 == 0)) {
            ESP_LOGI(TAG, "OTA Progress: %lu KB written", current_kb);
            last_progress_kb = current_kb;
        }
    }

    return ESP_OK;
}

/**
 * @brief Complete OTA process (Standard ESP-IDF esp_ota_end + esp_ota_set_boot_partition)
 */
esp_err_t ota_manager_complete(void)
{
    if (!ota_in_progress) {
        ESP_LOGE(TAG, "OTA not in progress");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Completing OTA update");

    /* Standard ESP-IDF OTA completion */
    ESP_LOGI(TAG, "ESP-IDF will perform built-in integrity verification");

    /* End OTA */
    esp_err_t err = esp_ota_end(ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_end failed: %s", esp_err_to_name(err));
        ble_ota_send_status(OTA_STATUS_ERROR, OTA_ERROR_VERIFY_FAILED, 0);
        return err;
    }

    /* Set boot partition */
    err = esp_ota_set_boot_partition(ota_partition);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_set_boot_partition failed: %s", esp_err_to_name(err));
        ble_ota_send_status(OTA_STATUS_ERROR, OTA_ERROR_VERIFY_FAILED, 0);
        return err;
    }

    ESP_LOGI(TAG, "OTA update completed successfully");
    ble_ota_send_status(OTA_STATUS_SUCCESS, OTA_ERROR_NONE, 0);
    
    /* Give time for status to be sent */
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    ESP_LOGI(TAG, "Restarting system...");
    esp_restart();
    
    return ESP_OK; // Never reached
}

/**
 * @brief Handle OTA command (Standard ESP-IDF approach)
 */
esp_err_t ota_manager_handle_command(ota_command_t cmd)
{
    ESP_LOGI(TAG, "Handling OTA command: %d", cmd);
    
    esp_err_t err = ESP_OK;
    
    switch (cmd) {
    case OTA_CMD_START:
        err = ota_manager_start();
        break;

    case OTA_CMD_STOP:
        ESP_LOGI(TAG, "STOP command received, stopping OTA...");
        err = ota_manager_stop();
        break;

    case OTA_CMD_END:
        ESP_LOGI(TAG, "END command received, completing OTA...");
        err = ota_manager_complete();
        break;

    case OTA_CMD_ABORT:
        if (ota_in_progress) {
            esp_ota_abort(ota_handle);
            ota_in_progress = false;
            ESP_LOGI(TAG, "OTA aborted");
        }
        break;

    default:
        ESP_LOGE(TAG, "Unknown OTA command: %d", cmd);
        err = ESP_ERR_INVALID_ARG;
        break;
    }
    
    return err;
}

/**
 * @brief Stop/abort OTA process
 */
esp_err_t ota_manager_stop(void)
{
    if (ota_in_progress) {
        esp_ota_abort(ota_handle);
        ota_in_progress = false;
        received_chunks = 0;
        binary_file_length = 0;
        image_header_was_checked = false;
        ESP_LOGI(TAG, "OTA stopped and state reset");
    }
    return ESP_OK;
}

/**
 * @brief Reset OTA state (for recovery from stuck states)
 */
esp_err_t ota_manager_reset(void)
{
    if (ota_in_progress) {
        esp_ota_abort(ota_handle);
    }
    ota_in_progress = false;
    received_chunks = 0;
    binary_file_length = 0;
    image_header_was_checked = false;
    expected_firmware_size = 0;
    total_chunks = 0;
    ESP_LOGI(TAG, "OTA state completely reset");
    return ESP_OK;
}

/**
 * @brief Get OTA progress information
 */
esp_err_t ota_manager_get_progress(uint32_t *bytes_received, uint32_t *total_size)
{
    if (bytes_received) {
        *bytes_received = binary_file_length;
    }
    if (total_size) {
        *total_size = 0; // Not tracked in standard implementation
    }
    return ESP_OK;
}

/**
 * @brief Get current OTA status (for BLE service compatibility)
 */
ota_status_t ota_manager_get_status(void)
{
    if (ota_in_progress) {
        return OTA_STATUS_ACK; // In progress
    } else {
        return OTA_STATUS_SUCCESS; // Idle/ready
    }
}

/**
 * @brief Get bytes received so far (for BLE service compatibility)
 */
uint32_t ota_manager_get_bytes_received(void)
{
    return binary_file_length;
}

/**
 * @brief Get detailed status response (for BLE service compatibility)
 */
void ota_manager_get_status_response(ota_status_response_t *response)
{
    if (response) {
        response->status = ota_manager_get_status();
        response->error = OTA_ERROR_NONE;
        response->bytes_received = binary_file_length;
        response->sequence_number = 0; // Not tracked in standard implementation
    }
}

/**
 * @brief Set firmware information (for BLE service compatibility)
 */
esp_err_t ota_manager_set_firmware_info(const ota_firmware_info_t *info)
{
    if (info) {
        expected_firmware_size = info->total_size;
        ESP_LOGI(TAG, "Firmware info received - Size: %lu bytes", info->total_size);
        ESP_LOGI(TAG, "Using ESP-IDF built-in integrity verification");
        return ESP_OK;
    }
    return ESP_ERR_INVALID_ARG;
}

/**
 * @brief Get firmware information (for BLE service compatibility)
 */
esp_err_t ota_manager_get_firmware_info(ota_firmware_info_t *info)
{
    if (info) {
        // Return current app info
        const esp_app_desc_t *app_desc = esp_app_get_description();
        memset(info, 0, sizeof(ota_firmware_info_t));
        strncpy((char *)info->version, app_desc->version, sizeof(info->version) - 1);
        info->total_size = binary_file_length; // Current transfer size
        return ESP_OK;
    }
    return ESP_ERR_INVALID_ARG;
}
