@: [LEN: ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1578
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd04
load:0x403cb700,len:0x2f00
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (27) boot: compile time Jul 26 2025 18:44:41[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v0.2[0m
[0;32mI (30) boot: efuse block revision: v1.3[0m
[0;32mI (33) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (37) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (41) boot.esp32s3: SPI Flash Size : 16MB[0m
[0;32mI (45) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (52) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (58) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (65) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (71) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (78) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (84) boot: End of partition table[0m
[0;32mI (88) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18658h ( 99928) map[0m
[0;32mI (113) esp_image: segment 1: paddr=00028680 vaddr=3fc9b900 size=04a94h ( 19092) load[0m
[0;32mI (117) esp_image: segment 2: paddr=0002d11c vaddr=40374000 size=02efch ( 12028) load[0m
[0;32mI (120) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=6203ch (401468) map[0m
[0;32mI (195) esp_image: segment 4: paddr=00092064 vaddr=40376efc size=149e0h ( 84448) load[0m
[0;32mI (213) esp_image: segment 5: paddr=000a6a4c vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (223) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (223) boot: Disabling RNG early entropy source...[0m
[0;32mI (233) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (233) octal_psram: dev id       : 0x02 (generation 3)[0m
[0;32mI (233) octal_psram: density      : 0x03 (64 Mbit)[0m
[0;32mI (236) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (240) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (244) octal_psram: VCC          : 0x01 (3V)[0m
[0;32mI (248) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (253) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (258) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (263) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (268) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (273) MSPI Timing: PSRAM timing tuning index: 4[0m
[0;32mI (276) esp_psram: Found 8MB PSRAM device[0m
[0;32mI (280) esp_psram: Speed: 80MHz[0m
[0;32mI (283) cpu_start: Multicore app[0m
[0;32mI (712) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (729) cpu_start: Pro cpu start user code[0m
[0;32mI (729) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (729) app_init: Application information:[0m
[0;32mI (729) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (735) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (741) app_init: Compile time:     Jul 26 2025 18:43:34[0m
[0;32mI (746) app_init: ELF file SHA256:  cb4f776fb...[0m
[0;32mI (750) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (754) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (758) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (762) efuse_init: Chip rev:         v0.2[0m
[0;32mI (766) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (772) heap_init: At 3FCAB6C8 len 0003E048 (248 KiB): RAM[0m
[0;32mI (777) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (782) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (787) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (793) esp_psram: Adding pool of 7929K of PSRAM memory to heap allocator[0m
[0;32mI (800) spi_flash: detected chip: boya[0m
[0;32mI (803) spi_flash: flash io: dio[0m
[0;32mI (806) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (812) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (819) coexist: coex firmware version: e727207[0m
[0;32mI (843) coexist: coexist rom version e7ae62f[0m
[0;32mI (843) main_task: Started on CPU0[0m
[0;32mI (853) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (853) main_task: Calling app_main()[0m
[0;32mI (863) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (863) BLE_INIT: Bluetooth MAC: a0:85:e3:f1:8c:c6[0m
[0;32mI (863) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (903) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(0)[0m
[0;32mI (973) NimBLE_BLE_PRPH: BLE Security initialized - Bonding: enabled, SC: enabled[0m
[0;32mI (973) uart: queue free spaces: 80[0m
[0;32mI (973) FLASH: : 0[0m
[0;32mI (973) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (973) HEAP: Free heap: 8294808[0m
[0;32mI (983) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (983) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (983) NEXUS_HANDLE: : TASK CREATED[0m
[0;32mI (993) NimBLE: Device Address: [0m
[0;32mI (993) NimBLE: a0:85:e3:f1:8c:c6[0m
[0;32mI (1003) NimBLE: [0m

[0;32mI (983) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1013) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1013) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1023) UART_READ: : ERROR[0m
0xFA
[0;32mI (1033) UART READ: [LEN: ]: 1[0m
[0;32mI (1033) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1043) main_task: Returned from app_main()[0m
[0;32mI (1043) UART_READ: : ERROR[0m
0xFE
[0;32mI (1043) UART READ: [LEN: ]: 1[0m
[0;32mI (1163) UART_READ: : ERROR[0m
0xFE
[0;32mI (1163) UART READ: [LEN: ]: 1[0m
[0;32mI (1223) UART_READ: : ERROR[0m
0xFF
[0;32mI (1223) UART READ: [LEN: ]: 1[0m
[0;32mI (1263) UART_READ: : ERROR[0m
0xFF
[0;32mI (1263) UART READ: [LEN: ]: 1[0m
[0;32mI (1283) UART_READ: : ERROR[0m
0xFF
[0;32mI (1283) UART READ: [LEN: ]: 1[0m
[0;32mI (1343) UART_READ: : ERROR[0m
0xFF
[0;32mI (1343) UART READ: [LEN: ]: 1[0m
[0;32mI (1383) UART_READ: : ERROR[0m
0xFF
[0;32mI (1383) UART READ: [LEN: ]: 1[0m
[0;32mI (1403) UART_READ: : ERROR[0m
0xFE
[0;32mI (1403) UART READ: [LEN: ]: 1[0m
[0;32mI (1423) UART_READ: : ERROR[0m
0xFE
[0;32mI (1423) UART READ: [LEN: ]: 1[0m
[0;32mI (1443) UART_READ: : ERROR[0m
0xFF
[0;32mI (1443) UART READ: [LEN: ]: 1[0m
[0;32mI (1503) UART_READ: : ERROR[0m
0xFD
[0;32mI (1503) UART READ: [LEN: ]: 1[0m
[0;32mI (1543) UART_READ: : ERROR[0m
0xFF
[0;32mI (1543) UART READ: [LEN: ]: 1[0m
[0;32mI (1563) UART_READ: : ERROR[0m
0xFF
[0;32mI (1563) UART READ: [LEN: ]: 1[0m
[0;32mI (1603) UART_READ: : ERROR[0m
0xFF
[0;32mI (1603) UART READ: [LEN: ]: 1[0m
[0;32mI (1643) UART_READ: : ERROR[0m
0xFB
[0;32mI (1643) UART READ: [LEN: ]: 1[0m
[0;32mI (1683) UART_READ: : ERROR[0m
0xFF
[0;32mI (1683) UART READ: [LEN: ]: 1[0m
[0;32mI (1723) UART_READ: : ERROR[0m
0xF9
[0;32mI (1723) UART READ: [LEN: ]: 1[0m
[0;32mI (1743) UART_READ: : ERROR[0m
0xFF
[0;32mI (1743) UART READ: [LEN: ]: 1[0m
[0;32mI (1763) UART_READ: : ERROR[0m
0xFF
[0;32mI (1763) UART READ: [LEN: ]: 1[0m
[0;32mI (1783) UART_READ: : ERROR[0m
0xFF
[0;32mI (1783) UART READ: [LEN: ]: 1[0m
[0;32mI (1823) UART_READ: : ERROR[0m
0xFB
[0;32mI (1823) UART READ: [LEN: ]: 1[0m
[0;32mI (1843) UART_READ: : ERROR[0m
0xFF
[0;32mI (1843) UART READ: [LEN: ]: 1[0m
[0;32mI (1883) UART_READ: : ERROR[0m
0xFE
[0;32mI (1883) UART READ: [LEN: ]: 1[0m
[0;32mI (1903) UART_READ: : ERROR[0m
0xFF
[0;32mI (1903) UART READ: [LEN: ]: 1[0m
[0;32mI (1943) UART_READ: : ERROR[0m
0xFD
[0;32mI (1943) UART READ: [LEN: ]: 1[0m
[0;32mI (2043) UART_READ: : ERROR[0m
0xFF
[0;32mI (2043) UART READ: [LEN: ]: 1[0m
[0;32mI (2103) UART_READ: : ERROR[0m
0xFF
[0;32mI (2103) UART READ: [LEN: ]: 1[0m
[0;32mI (2123) UART_READ: : ERROR[0m
0xFF
[0;32mI (2123) UART READ: [LEN: ]: 1[0m
[0;32mI (2143) UART_READ: : ERROR[0m
0xFF
[0;32mI (2143) UART READ: [LEN: ]: 1[0m
[0;32mI (2183) UART_READ: : ERROR[0m
0xFF
[0;32mI (2183) UART READ: [LEN: ]: 1[0m
[0;32mI (2203) UART_READ: : ERROR[0m
0xFF
[0;32mI (2203) UART READ: [LEN: ]: 1[0m
[0;32mI (2223) UART_READ: : ERROR[0m
0xFF
[0;32mI (2223) UART READ: [LEN: ]: 1[0m
[0;32mI (2243) UART_READ: : ERROR[0m
0xFF
[0;32mI (2243) UART READ: [LEN: ]: 1[0m
[0;32mI (2263) UART_READ: : ERROR[0m
0xFF
[0;32mI (2263) UART READ: [LEN: ]: 1[0m
[0;32mI (2303) UART_READ: : ERROR[0m
0xFF
[0;32mI (2303) UART READ: [LEN: ]: 1[0m
[0;32mI (2343) UART_READ: : ERROR[0m
0xFF
[0;32mI (2343) UART READ: [LEN: ]: 1[0m
[0;32mI (2383) UART_READ: : ERROR[0m
0xFF
[0;32mI (2383) UART READ: [LEN: ]: 1[0m
[0;32mI (2403) UART_READ: : ERROR[0m
0xFD
[0;32mI (2403) UART READ: [LEN: ]: 1[0m
[0;32mI (2423) UART_READ: : ERROR[0m
0xFF
[0;32mI (2423) UART READ: [LEN: ]: 1[0m
[0;32mI (2443) UART_READ: : ERROR[0m
0xFF
[0;32mI (2443) UART READ: [LEN: ]: 1[0m
[0;32mI (2463) UART_READ: : ERROR[0m
0xFF
[0;32mI (2463) UART READ: [LEN: ]: 1[0m
[0;32mI (2483) UART_READ: : ERROR[0m
0xFF
[0;32mI (2483) UART READ: [LEN: ]: 1[0m
[0;32mI (2543) UART_READ: : ERROR[0m
0xFF
[0;32mI (2543) UART READ: [LEN: ]: 1[0m
[0;32mI (2603) UART_READ: : ERROR[0m
0xFF
[0;32mI (2603) UART READ: [LEN: ]: 1[0m
[0;32mI (2623) UART_READ: : ERROR[0m
0xFF
[0;32mI (2623) UART READ: [LEN: ]: 1[0m
[0;32mI (2643) UART_READ: : ERROR[0m
0xFD
[0;32mI (2643) UART READ: [LEN: ]: 1[0m
[0;32mI (2663) UART_READ: : ERROR[0m
0xFF
[0;32mI (2663) UART READ: [LEN: ]: 1[0m
[0;32mI (2683) UART_READ: : ERROR[0m
0xFF
[0;32mI (2683) UART READ: [LEN: ]: 1[0m
[0;32mI (2703) UART_READ: : ERROR[0m
0xFE
[0;32mI (2703) UART READ: [LEN: ]: 1[0m
[0;32mI (2743) UART_READ: : ERROR[0m
0xFF
[0;32mI (2743) UART READ: [LEN: ]: 1[0m
[0;32mI (2783) UART_READ: : ERROR[0m
0xFB
[0;32mI (2783) UART READ: [LEN: ]: 1[0m
[0;32mI (2823) UART_READ: : ERROR[0m
0xFF
[0;32mI (2823) UART READ: [LEN: ]: 1[0m
[0;32mI (2883) UART_READ: : ERROR[0m
0xFF
[0;32mI (2883) UART READ: [LEN: ]: 1[0m
[0;32mI (2943) UART_READ: : ERROR[0m
0xFF
[0;32mI (2943) UART READ: [LEN: ]: 1[0m
[0;32mI (3023) UART_READ: : ERROR[0m
0xFF
[0;32mI (3023) UART READ: [LEN: ]: 1[0m
[0;32mI (3043) UART_READ: : ERROR[0m
0xFF
[0;32mI (3043) UART READ: [LEN: ]: 1[0m
[0;32mI (3103) UART_READ: : ERROR[0m
0xFF
[0;32mI (3103) UART READ: [LEN: ]: 1[0m
[0;32mI (3123) UART_READ: : ERROR[0m
0xFF
[0;32mI (3123) UART READ: [LEN: ]: 1[0m
[0;32mI (3143) UART_READ: : ERROR[0m
0xFF
[0;32mI (3143) UART READ: [LEN: ]: 1[0m
[0;32mI (3183) UART_READ: : ERROR[0m
0xFF
[0;32mI (3183) UART READ: [LEN: ]: 1[0m
[0;32mI (3203) UART_READ: : ERROR[0m
0xFF
[0;32mI (3203) UART READ: [LEN: ]: 1[0m
[0;32mI (3223) UART_READ: : ERROR[0m
0xFF
[0;32mI (3223) UART READ: [LEN: ]: 1[0m
[0;32mI (3243) UART_READ: : ERROR[0m
0xFF
[0;32mI (3243) UART READ: [LEN: ]: 1[0m
[0;32mI (3263) UART_READ: : ERROR[0m
0xFF
[0;32mI (3263) UART READ: [LEN: ]: 1[0m
[0;32mI (3303) UART_READ: : ERROR[0m
0xFF
[0;32mI (3303) UART READ: [LEN: ]: 1[0m
[0;32mI (3343) UART_READ: : ERROR[0m
0xFF
[0;32mI (3343) UART READ: [LEN: ]: 1[0m
[0;32mI (3403) UART_READ: : ERROR[0m
0xFF
[0;32mI (3403) UART READ: [LEN: ]: 1[0m
[0;32mI (3423) UART_READ: : ERROR[0m
0xFF
[0;32mI (3423) UART READ: [LEN: ]: 1[0m
[0;32mI (3443) UART_READ: : ERROR[0m
0xFD
[0;32mI (3443) UART READ: [LEN: ]: 1[0m
[0;32mI (3463) UART_READ: : ERROR[0m
0xFF
[0;32mI (3463) UART READ: [LEN: ]: 1[0m
[0;32mI (3543) UART_READ: : ERROR[0m
0xFF
[0;32mI (3543) UART READ: [LEN: ]: 1[0m
[0;32mI (3563) UART_READ: : ERROR[0m
0xFE
[0;32mI (3563) UART READ: [LEN: ]: 1[0m
[0;32mI (3583) UART_READ: : ERROR[0m
0xFF
[0;32mI (3583) UART READ: [LEN: ]: 1[0m
[0;32mI (3663) UART_READ: : ERROR[0m
0xFF
[0;32mI (3663) UART READ: [LEN: ]: 1[0m
[0;32mI (3703) UART_READ: : ERROR[0m
0xFF
[0;32mI (3703) UART READ: [LEN: ]: 1[0m
[0;32mI (3723) UART_READ: : ERROR[0m
0xFF
[0;32mI (3723) UART READ: [LEN: ]: 1[0m
[0;32mI (3763) UART_READ: : ERROR[0m
0xFF
[0;32mI (3763) UART READ: [LEN: ]: 1[0m
[0;32mI (3843) UART_READ: : ERROR[0m
0xFF
[0;32mI (3843) UART READ: [LEN: ]: 1[0m
[0;32mI (3883) UART_READ: : ERROR[0m
0xFF
[0;32mI (3883) UART READ: [LEN: ]: 1[0m
[0;32mI (3943) UART_READ: : ERROR[0m
0xFF
[0;32mI (3943) UART READ: [LEN: ]: 1[0m
[0;32mI (3963) UART_READ: : ERROR[0m
0xFF
[0;32mI (3963) UART READ: [LEN: ]: 1[0m
[0;32mI (3983) UART_READ: : ERROR[0m
0xFF
[0;32mI (3983) UART READ: [LEN: ]: 1[0m
[0;32mI (4003) UART_READ: : ERROR[0m
0xFF
[0;32mI (4003) UART READ: [LEN: ]: 1[0m
[0;32mI (4063) UART_READ: : ERROR[0m
0xFF
[0;32mI (4063) UART READ: [LEN: ]: 1[0m
[0;32mI (4083) UART_READ: : ERROR[0m
0xFF
[0;32mI (4083) UART READ: [LEN: ]: 1[0m
[0;32mI (4103) UART_READ: : ERROR[0m
0xFF
[0;32mI (4103) UART READ: [LEN: ]: 1[0m
[0;32mI (4123) UART_READ: : ERROR[0m
0xFF
[0;32mI (4123) UART READ: [LEN: ]: 1[0m
[0;32mI (4143) UART_READ: : ERROR[0m
0xFF
[0;32mI (4143) UART READ: [LEN: ]: 1[0m
[0;32mI (4183) UART_READ: : ERROR[0m
0xFF
[0;32mI (4183) UART READ: [LEN: ]: 1[0m
[0;32mI (4203) UART_READ: : ERROR[0m
0xFF
[0;32mI (4203) UART READ: [LEN: ]: 1[0m
[0;32mI (4223) UART_READ: : ERROR[0m
0xFF
[0;32mI (4223) UART READ: [LEN: ]: 1[0m
[0;32mI (4263) UART_READ: : ERROR[0m
0xFF
[0;32mI (4263) UART READ: [LEN: ]: 1[0m
[0;32mI (4283) UART_READ: : ERROR[0m
0xFF
[0;32mI (4283) UART READ: [LEN: ]: 1[0m
[0;32mI (4303) UART_READ: : ERROR[0m
0xFF
[0;32mI (4303) UART READ: [LEN: ]: 1[0m
[0;32mI (4323) UART_READ: : ERROR[0m
0xFF
[0;32mI (4323) UART READ: [LEN: ]: 1[0m
[0;32mI (4343) UART_READ: : ERROR[0m
0xFF
[0;32mI (4343) UART READ: [LEN: ]: 1[0m
[0;32mI (4363) UART_READ: : ERROR[0m
0xFF
[0;32mI (4363) UART READ: [LEN: ]: 1[0m
[0;32mI (4403) UART_READ: : ERROR[0m
0xFF
[0;32mI (4403) UART READ: [LEN: ]: 1[0m
[0;32mI (4443) UART_READ: : ERROR[0m
0xFF
[0;32mI (4443) UART READ: [LEN: ]: 1[0m
[0;32mI (4523) UART_READ: : ERROR[0m
0xFF
[0;32mI (4523) UART READ: [LEN: ]: 1[0m
[0;32mI (4543) UART_READ: : ERROR[0m
0xFF
[0;32mI (4543) UART READ: [LEN: ]: 1[0m
[0;32mI (4563) UART_READ: : ERROR[0m
0xFF
[0;32mI (4563) UART READ: [LEN: ]: 1[0m
[0;32mI (4583) UART_READ: : ERROR[0m
0xFF
[0;32mI (4583) UART READ: [LEN: ]: 1[0m
[0;32mI (4603) UART_READ: : ERROR[0m
0xFF
[0;32mI (4603) UART READ: [LEN: ]: 1[0m
[0;32mI (4643) UART_READ: : ERROR[0m
0xFE
[0;32mI (4643) UART READ: [LEN: ]: 1[0m
[0;32mI (4663) UART_READ: : ERROR[0m
0xFF
[0;32mI (4663) UART READ: [LEN: ]: 1[0m
[0;32mI (4703) UART_READ: : ERROR[0m
0xFF
[0;32mI (4703) UART READ: [LEN: ]: 1[0m
[0;32mI (4723) UART_READ: : ERROR[0m
0xFF
[0;32mI (4723) UART READ: [LEN: ]: 1[0m
[0;32mI (4743) UART_READ: : ERROR[0m
0xFE
[0;32mI (4743) UART READ: [LEN: ]: 1[0m
[0;32mI (4783) UART_READ: : ERROR[0m
0xFF
[0;32mI (4783) UART READ: [LEN: ]: 1[0m
[0;32mI (4803) UART_READ: : ERROR[0m
0xFF
[0;32mI (4803) UART READ: [LEN: ]: 1[0m
[0;32mI (4843) UART_READ: : ERROR[0m
0xFF
[0;32mI (4843) UART READ: [LEN: ]: 1[0m
[0;32mI (4923) UART_READ: : ERROR[0m
0xFF
[0;32mI (4923) UART READ: [LEN: ]: 1[0m
[0;32mI (4943) UART_READ: : ERROR[0m
0xFF
[0;32mI (4943) UART READ: [LEN: ]: 1[0m
[0;32mI (4983) UART_READ: : ERROR[0m
0xFF
[0;32mI (4983) UART READ: [LEN: ]: 1[0m
[0;32mI (5023) UART_READ: : ERROR[0m
0xFF
[0;32mI (5023) UART READ: [LEN: ]: 1[0m
[0;32mI (5043) UART_READ: : ERROR[0m
0xFF
[0;32mI (5043) UART READ: [LEN: ]: 1[0m
[0;32mI (5063) UART_READ: : ERROR[0m
0xFF
[0;32mI (5063) UART READ: [LEN: ]: 1[0m
[0;32mI (5103) UART_READ: : ERROR[0m
0xFF
[0;32mI (5103) UART READ: [LEN: ]: 1[0m
[0;32mI (5143) UART_READ: : ERROR[0m
0xFF
[0;32mI (5143) UART READ: [LEN: ]: 1[0m
[0;32mI (5163) UART_READ: : ERROR[0m
0xFF
[0;32mI (5163) UART READ: [LEN: ]: 1[0m
[0;32mI (5203) UART_READ: : ERROR[0m
0xFF
[0;32mI (5203) UART READ: [LEN: ]: 1[0m
[0;32mI (5243) UART_READ: : ERROR[0m
0xFE
[0;32mI (5243) UART READ: [LEN: ]: 1[0m
[0;32mI (5263) UART_READ: : ERROR[0m
0xFF
[0;32mI (5263) UART READ: [LEN: ]: 1[0m
[0;32mI (5283) UART_READ: : ERROR[0m
0xFF
[0;32mI (5283) UART READ: [LEN: ]: 1[0m
[0;32mI (5323) UART_READ: : ERROR[0m
0xFF
[0;32mI (5323) UART READ: [LEN: ]: 1[0m
[0;32mI (5343) UART_READ: : ERROR[0m
0xFF
[0;32mI (5343) UART READ: [LEN: ]: 1[0m
[0;32mI (5363) UART_READ: : ERROR[0m
0xFF
[0;32mI (5363) UART READ: [LEN: ]: 1[0m
[0;32mI (5403) UART_READ: : ERROR[0m
0xFF
[0;32mI (5403) UART READ: [LEN: ]: 1[0m
[0;32mI (5423) UART_READ: : ERROR[0m
0xFF
[0;32mI (5423) UART READ: [LEN: ]: 1[0m
[0;32mI (5443) UART_READ: : ERROR[0m
0xFF
[0;32mI (5443) UART READ: [LEN: ]: 1[0m
[0;32mI (5503) UART_READ: : ERROR[0m
0xFF
[0;32mI (5503) UART READ: [LEN: ]: 1[0m
[0;32mI (5543) UART_READ: : ERROR[0m
0xFE
[0;32mI (5543) UART READ: [LEN: ]: 1[0m
[0;32mI (5563) UART_READ: : ERROR[0m
0xFF
[0;32mI (5563) UART READ: [LEN: ]: 1[0m
[0;32mI (5583) UART_READ: : ERROR[0m
0xFF
[0;32mI (5583) UART READ: [LEN: ]: 1[0m
[0;32mI (5603) UART_READ: : ERROR[0m
0xFF
[0;32mI (5603) UART READ: [LEN: ]: 1[0m
[0;32mI (5643) UART_READ: : ERROR[0m
0xFF
[0;32mI (5643) UART READ: [LEN: ]: 1[0m
[0;32mI (5663) UART_READ: : ERROR[0m
0xFF
[0;32mI (5663) UART READ: [LEN: ]: 1[0m
[0;32mI (5703) UART_READ: : ERROR[0m
0xFF
[0;32mI (5703) UART READ: [LEN: ]: 1[0m
[0;32mI (5723) UART_READ: : ERROR[0m
0xFF
[0;32mI (5723) UART READ: [LEN: ]: 1[0m
[0;32mI (5743) UART_READ: : ERROR[0m
0xFE
[0;32mI (5743) UART READ: [LEN: ]: 1[0m
[0;32mI (5763) UART_READ: : ERROR[0m
0xFF
[0;32mI (5763) UART READ: [LEN: ]: 1[0m
[0;32mI (5803) UART_READ: : ERROR[0m
0xFF
[0;32mI (5803) UART READ: [LEN: ]: 1[0m
[0;32mI (5843) UART_READ: : ERROR[0m
0xFE
[0;32mI (5843) UART READ: [LEN: ]: 1[0m
[0;32mI (5863) UART_READ: : ERROR[0m
0xFF
[0;32mI (5863) UART READ: [LEN: ]: 1[0m
[0;32mI (5903) UART_READ: : ERROR[0m
0xFF
[0;32mI (5903) UART READ: [LEN: ]: 1[0m
[0;32mI (5923) UART_READ: : ERROR[0m
0xFF
[0;32mI (5923) UART READ: [LEN: ]: 1[0m
[0;32mI (5943) UART_READ: : ERROR[0m
0xFF
[0;32mI (5943) UART READ: [LEN: ]: 1[0m
[0;32mI (6043) UART_READ: : ERROR[0m
0xFF
[0;32mI (6043) UART READ: [LEN: ]: 1[0m
[0;32mI (6063) UART_READ: : ERROR[0m
0xFF
[0;32mI (6063) UART READ: [LEN: ]: 1[0m
[0;32mI (6103) UART_READ: : ERROR[0m
0xFF
[0;32mI (6103) UART READ: [LEN: ]: 1[0m
[0;32mI (6123) UART_READ: : ERROR[0m
0xFF
[0;32mI (6123) UART READ: [LEN: ]: 1[0m
[0;32mI (6143) UART_READ: : ERROR[0m
0xFE
[0;32mI (6143) UART READ: [LEN: ]: 1[0m
[0;32mI (6163) UART_READ: : ERROR[0m
0xFF
[0;32mI (6163) UART READ: [LEN: ]: 1[0m
[0;32mI (6183) UART_READ: : ERROR[0m
0xFF
[0;32mI (6183) UART READ: [LEN: ]: 1[0m
[0;32mI (6203) UART_READ: : ERROR[0m
0xFF
[0;32mI (6203) UART READ: [LEN: ]: 1[0m
[0;32mI (6223) UART_READ: : ERROR[0m
0xFF
[0;32mI (6223) UART READ: [LEN: ]: 1[0m
[0;32mI (6243) UART_READ: : ERROR[0m
0xFF
[0;32mI (6243) UART READ: [LEN: ]: 1[0m
[0;32mI (6263) UART_READ: : ERROR[0m
0xFF
[0;32mI (6263) UART READ: [LEN: ]: 1[0m
[0;32mI (6283) UART_READ: : ERROR[0m
0xFF
[0;32mI (6283) UART READ: [LEN: ]: 1[0m
[0;32mI (6323) UART_READ: : ERROR[0m
0xFF
[0;32mI (6323) UART READ: [LEN: ]: 1[0m
[0;32mI (6343) UART_READ: : ERROR[0m
0xFF
[0;32mI (6343) UART READ: [LEN: ]: 1[0m
[0;32mI (6403) UART_READ: : ERROR[0m
0xFF
[0;32mI (6403) UART READ: [LEN: ]: 1[0m
[0;32mI (6423) UART_READ: : ERROR[0m
0xFF
[0;32mI (6423) UART READ: [LEN: ]: 1[0m
[0;32mI (6443) UART_READ: : ERROR[0m
0xFE
[0;32mI (6443) UART READ: [LEN: ]: 1[0m
[0;32mI (6463) UART_READ: : ERROR[0m
0xFF
[0;32mI (6463) UART READ: [LEN: ]: 1[0m
[0;32mI (6483) UART_READ: : ERROR[0m
0xFF
[0;32mI (6483) UART READ: [LEN: ]: 1[0m
[0;32mI (6523) UART_READ: : ERROR[0m
0xFF
[0;32mI (6523) UART READ: [LEN: ]: 1[0m
[0;32mI (6543) UART_READ: : ERROR[0m
0xFF
[0;32mI (6543) UART READ: [LEN: ]: 1[0m
[0;32mI (6563) UART_READ: : ERROR[0m
0xFF
[0;32mI (6563) UART READ: [LEN: ]: 1[0m
[0;32mI (6583) UART_READ: : ERROR[0m
0xFF
[0;32mI (6583) UART READ: [LEN: ]: 1[0m
[0;32mI (6623) UART_READ: : ERROR[0m
0xFF
[0;32mI (6623) UART READ: [LEN: ]: 1[0m
[0;32mI (6643) UART_READ: : ERROR[0m
0xFF
[0;32mI (6643) UART READ: [LEN: ]: 1[0m
[0;32mI (6663) UART_READ: : ERROR[0m
0xFF
[0;32mI (6663) UART READ: [LEN: ]: 1[0m
[0;32mI (6683) UART_READ: : ERROR[0m
0xFF
[0;32mI (6683) UART READ: [LEN: ]: 1[0m
[0;32mI (6743) UART_READ: : ERROR[0m
0xFF
[0;32mI (6743) UART READ: [LEN: ]: 1[0m
[0;32mI (6763) UART_READ: : ERROR[0m
0xFF
[0;32mI (6763) UART READ: [LEN: ]: 1[0m
[0;32mI (6783) UART_READ: : ERROR[0m
0xFF
[0;32mI (6783) UART READ: [LEN: ]: 1[0m
[0;32mI (6803) UART_READ: : ERROR[0m
0xFF
[0;32mI (6803) UART READ: [LEN: ]: 1[0m
[0;32mI (6823) UART_READ: : ERROR[0m
0xFF
[0;32mI (6823) UART READ: [LEN: ]: 1[0m
[0;32mI (6843) UART_READ: : ERROR[0m
0xFF
[0;32mI (6843) UART READ: [LEN: ]: 1[0m
[0;32mI (6863) UART_READ: : ERROR[0m
0xFF
[0;32mI (6863) UART READ: [LEN: ]: 1[0m
[0;32mI (6883) UART_READ: : ERROR[0m
0xFF
[0;32mI (6883) UART READ: [LEN: ]: 1[0m
[0;32mI (6903) UART_READ: : ERROR[0m
0xFF
[0;32mI (6903) UART READ: [LEN: ]: 1[0m
[0;32mI (6923) UART_READ: : ERROR[0m
0xFF
[0;32mI (6923) UART READ: [LEN: ]: 1[0m
[0;32mI (6943) UART_READ: : ERROR[0m
0xFF
[0;32mI (6943) UART READ: [LEN: ]: 1[0m
[0;32mI (7003) UART_READ: : ERROR[0m
0xFF
[0;32mI (7003) UART READ: [LEN: ]: 1[0m
[0;32mI (7043) UART_READ: : ERROR[0m
0xFE
[0;32mI (7043) UART READ: [LEN: ]: 1[0m
[0;32mI (7083) UART_READ: : ERROR[0m
0xFF
[0;32mI (7083) UART READ: [LEN: ]: 1[0m
[0;32mI (7103) UART_READ: : ERROR[0m
0xFF
[0;32mI (7103) UART READ: [LEN: ]: 1[0m
[0;32mI (7123) UART_READ: : ERROR[0m
0xFF
[0;32mI (7123) UART READ: [LEN: ]: 1[0m
[0;32mI (7143) UART_READ: : ERROR[0m
0xFF
[0;32mI (7143) UART READ: [LEN: ]: 1[0m
[0;32mI (7213) UART_READ: : ERROR[0m
0xFF
[0;32mI (7223) UART READ: [LEN: ]: 1[0m
[0;32mI (7233) UART_READ: : ERROR[0m
0xFF
[0;32mI (7243) UART READ: [LEN: ]: 1[0m
[0;32mI (7253) UART_READ: : ERROR[0m
0xFF
[0;32mI (7263) UART READ: [LEN: ]: 1[0m
[0;32mI (7273) UART_READ: : ERROR[0m
0xFF
[0;32mI (7283) UART READ: [LEN: ]: 1[0m
[0;32mI (7293) UART_READ: : ERROR[0m
0xFF
[0;32mI (7303) UART READ: [LEN: ]: 1[0m
[0;32mI (7393) UART_READ: : ERROR[0m
0xFF
[0;32mI (7393) UART READ: [LEN: ]: 1[0m
[0;32mI (7413) UART_READ: : ERROR[0m
0xFF
[0;32mI (7413) UART READ: [LEN: ]: 1[0m
[0;32mI (7453) UART_READ: : ERROR[0m
0xFF
[0;32mI (7453) UART READ: [LEN: ]: 1[0m
[0;32mI (7473) UART_READ: : ERROR[0m
0xFF
[0;32mI (7473) UART READ: [LEN: ]: 1[0m
[0;32mI (7493) UART_READ: : ERROR[0m
0xFF
[0;32mI (7493) UART READ: [LEN: ]: 1[0m
[0;32mI (7513) UART_READ: : ERROR[0m
0xFF
[0;32mI (7513) UART READ: [LEN: ]: 1[0m
[0;32mI (7553) UART_READ: : ERROR[0m
0xFF
[0;32mI (7553) UART READ: [LEN: ]: 1[0m
[0;32mI (7573) UART_READ: : ERROR[0m
0xFF
[0;32mI (7573) UART READ: [LEN: ]: 1[0m
[0;32mI (7593) UART_READ: : ERROR[0m
0xFF
[0;32mI (7593) UART READ: [LEN: ]: 1[0m
[0;32mI (7613) UART_READ: : ERROR[0m
0xFF
[0;32mI (7613) UART READ: [LEN: ]: 1[0m
[0;32mI (7733) UART_READ: : ERROR[0m
0xFF
[0;32mI (7733) UART READ: [LEN: ]: 1[0m
[0;32mI (7753) UART_READ: : ERROR[0m
0xFF
[0;32mI (7753) UART READ: [LEN: ]: 1[0m
[0;32mI (7773) UART_READ: : ERROR[0m
0xFF
[0;32mI (7773) UART READ: [LEN: ]: 1[0m
[0;32mI (7873) UART_READ: : ERROR[0m
0xFF
[0;32mI (7873) UART READ: [LEN: ]: 1[0m
[0;32mI (7893) UART_READ: : ERROR[0m
0xFF
[0;32mI (7893) UART READ: [LEN: ]: 1[0m
[0;32mI (7933) UART_READ: : ERROR[0m
0xFF
[0;32mI (7933) UART READ: [LEN: ]: 1[0m
[0;32mI (7953) UART_READ: : ERROR[0m
0xFE
[0;32mI (7953) UART READ: [LEN: ]: 1[0m
[0;32mI (7973) UART_READ: : ERROR[0m
0xFF
[0;32mI (7973) UART READ: [LEN: ]: 1[0m
[0;32mI (7993) UART_READ: : ERROR[0m
0xFF
[0;32mI (7993) UART READ: [LEN: ]: 1[0m
[0;32mI (8053) UART_READ: : ERROR[0m
0xFF
[0;32mI (8053) UART READ: [LEN: ]: 1[0m
[0;32mI (8073) UART_READ: : ERROR[0m
0xFE
[0;32mI (8073) UART READ: [LEN: ]: 1[0m
[0;32mI (8093) UART_READ: : ERROR[0m
0xFF
[0;32mI (8093) UART READ: [LEN: ]: 1[0m
[0;32mI (8113) UART_READ: : ERROR[0m
0xFF
[0;32mI (8113) UART READ: [LEN: ]: 1[0m
[0;32mI (8133) UART_READ: : ERROR[0m
0xFF
[0;32mI (8133) UART READ: [LEN: ]: 1[0m
[0;32mI (8213) UART_READ: : ERROR[0m
0xFF
[0;32mI (8213) UART READ: [LEN: ]: 1[0m
[0;32mI (8233) UART_READ: : ERROR[0m
0xFF
[0;32mI (8233) UART READ: [LEN: ]: 1[0m
[0;32mI (8253) UART_READ: : ERROR[0m
0xFF
[0;32mI (8253) UART READ: [LEN: ]: 1[0m
[0;32mI (8333) UART_READ: : ERROR[0m
0xFF
[0;32mI (8333) UART READ: [LEN: ]: 1[0m
[0;32mI (8353) UART_READ: : ERROR[0m
0xFE
[0;32mI (8353) UART READ: [LEN: ]: 1[0m
[0;32mI (8373) UART_READ: : ERROR[0m
0xFE
[0;32mI (8373) UART READ: [LEN: ]: 1[0m
[0;32mI (8393) UART_READ: : ERROR[0m
0xFF
[0;32mI (8393) UART READ: [LEN: ]: 1[0m
[0;32mI (8433) UART_READ: : ERROR[0m
0xFF
[0;32mI (8433) UART READ: [LEN: ]: 1[0m
[0;32mI (8453) UART_READ: : ERROR[0m
0xFE
[0;32mI (8453) UART READ: [LEN: ]: 1[0m
[0;32mI (8473) UART_READ: : ERROR[0m
0xFF
[0;32mI (8473) UART READ: [LEN: ]: 1[0m
[0;32mI (8553) UART_READ: : ERROR[0m
0xFE
[0;32mI (8553) UART READ: [LEN: ]: 1[0m
[0;32mI (8573) UART_READ: : ERROR[0m
0xFE
[0;32mI (8573) UART READ: [LEN: ]: 1[0m
[0;32mI (8613) UART_READ: : ERROR[0m
0xFF
[0;32mI (8613) UART READ: [LEN: ]: 1[0m
[0;32mI (8633) UART_READ: : ERROR[0m
0xFF
[0;32mI (8633) UART READ: [LEN: ]: 1[0m
[0;32mI (8653) UART_READ: : ERROR[0m
0xFF
[0;32mI (8653) UART READ: [LEN: ]: 1[0m
[0;32mI (8673) UART_READ: : ERROR[0m
0xFF
[0;32mI (8673) UART READ: [LEN: ]: 1[0m
[0;32mI (8693) UART_READ: : ERROR[0m
0xFF
[0;32mI (8693) UART READ: [LEN: ]: 1[0m
[0;32mI (8733) UART_READ: : ERROR[0m
0xFF
[0;32mI (8733) UART READ: [LEN: ]: 1[0m
[0;32mI (8753) UART_READ: : ERROR[0m
0xFE
[0;32mI (8753) UART READ: [LEN: ]: 1[0m
[0;32mI (8773) UART_READ: : ERROR[0m
0xFF
[0;32mI (8773) UART READ: [LEN: ]: 1[0m
[0;32mI (8793) UART_READ: : ERROR[0m
0xFF
[0;32mI (8793) UART READ: [LEN: ]: 1[0m
[0;32mI (8853) UART_READ: : ERROR[0m
0xFE
[0;32mI (8853) UART READ: [LEN: ]: 1[0m
[0;32mI (8953) UART_READ: : ERROR[0m
0xFF
[0;32mI (8953) UART READ: [LEN: ]: 1[0m
[0;32mI (8973) UART_READ: : ERROR[0m
0xFF
[0;32mI (8973) UART READ: [LEN: ]: 1[0m
[0;32mI (8993) UART_READ: : ERROR[0m
0xFF
[0;32mI (8993) UART READ: [LEN: ]: 1[0m
[0;32mI (9073) UART_READ: : ERROR[0m
0xFF
[0;32mI (9073) UART READ: [LEN: ]: 1[0m
[0;32mI (9093) UART_READ: : ERROR[0m
0xFF
[0;32mI (9093) UART READ: [LEN: ]: 1[0m
[0;32mI (9153) UART_READ: : ERROR[0m
0xFF
[0;32mI (9153) UART READ: [LEN: ]: 1[0m
[0;32mI (9173) UART_READ: : ERROR[0m
0xFF
[0;32mI (9173) UART READ: [LEN: ]: 1[0m
[0;32mI (9193) UART_READ: : ERROR[0m
0xFF
[0;32mI (9193) UART READ: [LEN: ]: 1[0m
[0;32mI (9213) UART_READ: : ERROR[0m
0xFF
[0;32mI (9213) UART READ: [LEN: ]: 1[0m
[0;32mI (9253) UART_READ: : ERROR[0m
0xFF
[0;32mI (9253) UART READ: [LEN: ]: 1[0m
[0;32mI (9273) UART_READ: : ERROR[0m
0xFF
[0;32mI (9273) UART READ: [LEN: ]: 1[0m
[0;32mI (9333) UART_READ: : ERROR[0m
0xFF
[0;32mI (9333) UART READ: [LEN: ]: 1[0m
[0;32mI (9353) UART_READ: : ERROR[0m
0xFF
[0;32mI (9353) UART READ: [LEN: ]: 1[0m
[0;32mI (9373) UART_READ: : ERROR[0m
0xFF
[0;32mI (9373) UART READ: [LEN: ]: 1[0m
[0;32mI (9393) UART_READ: : ERROR[0m
0xFF
[0;32mI (9393) UART READ: [LEN: ]: 1[0m
[0;32mI (9413) UART_READ: : ERROR[0m
0xFF
[0;32mI (9413) UART READ: [LEN: ]: 1[0m
[0;32mI (9453) UART_READ: : ERROR[0m
0xFF
[0;32mI (9453) UART READ: [LEN: ]: 1[0m
[0;32mI (9493) UART_READ: : ERROR[0m
0xFF
[0;32mI (9493) UART READ: [LEN: ]: 1[0m
[0;32mI (9513) UART_READ: : ERROR[0m
0xFF
[0;32mI (9513) UART READ: [LEN: ]: 1[0m
[0;32mI (9533) UART_READ: : ERROR[0m
0xFF
[0;32mI (9533) UART READ: [LEN: ]: 1[0m
[0;32mI (9553) UART_READ: : ERROR[0m
0xFE
[0;32mI (9553) UART READ: [LEN: ]: 1[0m
[0;32mI (9573) UART_READ: : ERROR[0m
0xFF
[0;32mI (9573) UART READ: [LEN: ]: 1[0m
[0;32mI (9593) UART_READ: : ERROR[0m
0xFF
[0;32mI (9593) UART READ: [LEN: ]: 1[0m
[0;32mI (9633) UART_READ: : ERROR[0m
0xFF
[0;32mI (9633) UART READ: [LEN: ]: 1[0m
[0;32mI (9653) UART_READ: : ERROR[0m
0xFE
[0;32mI (9653) UART READ: [LEN: ]: 1[0m
[0;32mI (9673) UART_READ: : ERROR[0m
0xFF
[0;32mI (9673) UART READ: [LEN: ]: 1[0m
[0;32mI (9713) UART_READ: : ERROR[0m
0xFF
[0;32mI (9713) UART READ: [LEN: ]: 1[0m
[0;32mI (9733) UART_READ: : ERROR[0m
0xFF
[0;32mI (9733) UART READ: [LEN: ]: 1[0m
[0;32mI (9753) UART_READ: : ERROR[0m
0xFF
[0;32mI (9753) UART READ: [LEN: ]: 1[0m
[0;32mI (9793) UART_READ: : ERROR[0m
0xFF
[0;32mI (9793) UART READ: [LEN: ]: 1[0m
[0;32mI (9833) UART_READ: : ERROR[0m
0xFF
[0;32mI (9833) UART READ: [LEN: ]: 1[0m
[0;32mI (9893) UART_READ: : ERROR[0m
0xFF
[0;32mI (9893) UART READ: [LEN: ]: 1[0m
[0;32mI (9913) UART_READ: : ERROR[0m
0xFF
[0;32mI (9913) UART READ: [LEN: ]: 1[0m
[0;32mI (9953) UART_READ: : ERROR[0m
0xFF
[0;32mI (9953) UART READ: [LEN: ]: 1[0m
[0;32mI (9993) UART_READ: : ERROR[0m
0xFF
[0;32mI (9993) UART READ: [LEN: ]: 1[0m
[0;32mI (10013) UART_READ: : ERROR[0m
0xFF
[0;32mI (10013) UART READ: [LEN: ]: 1[0m
[0;32mI (10053) UART_READ: : ERROR[0m
0xFF
[0;32mI (10053) UART READ: [LEN: ]: 1[0m
[0;32mI (10073) UART_READ: : ERROR[0m
0xFE
[0;32mI (10073) UART READ: [LEN: ]: 1[0m
[0;32mI (10133) UART_READ: : ERROR[0m
0xFF
[0;32mI (10133) UART READ: [LEN: ]: 1[0m
[0;32mI (10173) UART_READ: : ERROR[0m
0xFF
[0;32mI (10173) UART READ: [LEN: ]: 1[0m
[0;32mI (10213) UART_READ: : ERROR[0m
0xFF
[0;32mI (10213) UART READ: [LEN: ]: 1[0m
[0;32mI (10273) UART_READ: : ERROR[0m
0xFF
[0;32mI (10273) UART READ: [LEN: ]: 1[0m
[0;32mI (10333) UART_READ: : ERROR[0m
0xFF
[0;32mI (10333) UART READ: [LEN: ]: 1[0m
[0;32mI (10433) UART_READ: : ERROR[0m
0xFF
[0;32mI (10433) UART READ: [LEN: ]: 1[0m
[0;32mI (10453) UART_READ: : ERROR[0m
0xFF
[0;32mI (10453) UART READ: [LEN: ]: 1[0m
[0;32mI (10473) UART_READ: : ERROR[0m
0xFF
[0;32mI (10473) UART READ: [LEN: ]: 1[0m
[0;32mI (10493) UART_READ: : ERROR[0m
0xFF
[0;32mI (10493) UART READ: [LEN: ]: 1[0m
[0;32mI (10533) UART_READ: : ERROR[0m
0xFE
[0;32mI (10533) UART READ: [LEN: ]: 1[0m
[0;32mI (10553) UART_READ: : ERROR[0m
0xFF
[0;32mI (10553) UART READ: [LEN: ]: 1[0m
[0;32mI (10593) UART_READ: : ERROR[0m
0xFF
[0;32mI (10593) UART READ: [LEN: ]: 1[0m
[0;32mI (10653) UART_READ: : ERROR[0m
0xFF
[0;32mI (10653) UART READ: [LEN: ]: 1[0m
[0;32mI (10693) UART_READ: : ERROR[0m
0xFF
[0;32mI (10693) UART READ: [LEN: ]: 1[0m
[0;32mI (10753) UART_READ: : ERROR[0m
0xFF
[0;32mI (10753) UART READ: [LEN: ]: 1[0m
[0;32mI (10833) UART_READ: : ERROR[0m
0xFF
[0;32mI (10833) UART READ: [LEN: ]: 1[0m
[0;32mI (10853) UART_READ: : ERROR[0m
0xFF
[0;32mI (10853) UART READ: [LEN: ]: 1[0m
[0;32mI (10873) UART_READ: : ERROR[0m
0xFF
[0;32mI (10873) UART READ: [LEN: ]: 1[0m
[0;32mI (10913) UART_READ: : ERROR[0m
0xFD
[0;32mI (10913) UART READ: [LEN: ]: 1[0m
[0;32mI (10953) UART_READ: : ERROR[0m
0xFF
[0;32mI (10953) UART READ: [LEN: ]: 1[0m
[0;32mI (11053) UART_READ: : ERROR[0m
0xFF
[0;32mI (11053) UART READ: [LEN: ]: 1[0m
[0;32mI (11153) UART_READ: : ERROR[0m
0xFF
[0;32mI (11153) UART READ: [LEN: ]: 1[0m
[0;32mI (11173) UART_READ: : ERROR[0m
0xFE
[0;32mI (11173) UART READ: [LEN: ]: 1[0m
[0;32mI (11293) UART_READ: : ERROR[0m
0xFF
[0;32mI (11293) UART READ: [LEN: ]: 1[0m
[0;32mI (11313) UART_READ: : ERROR[0m
0xFF
[0;32mI (11313) UART READ: [LEN: ]: 1[0m
[0;32mI (11333) UART_READ: : ERROR[0m
0xFF
[0;32mI (11333) UART READ: [LEN: ]: 1[0m
[0;32mI (11353) UART_READ: : ERROR[0m
0xFD
[0;32mI (11353) UART READ: [LEN: ]: 1[0m
[0;32mI (11373) UART_READ: : ERROR[0m
0xFF
[0;32mI (11373) UART READ: [LEN: ]: 1[0m
[0;32mI (11413) UART_READ: : ERROR[0m
0xFF
[0;32mI (11413) UART READ: [LEN: ]: 1[0m
[0;32mI (11573) UART_READ: : ERROR[0m
0xFF
[0;32mI (11573) UART READ: [LEN: ]: 1[0m
[0;32mI (11673) UART_READ: : ERROR[0m
0xFD
[0;32mI (11673) UART READ: [LEN: ]: 1[0m
[0;32mI (11713) UART_READ: : ERROR[0m
0xFD
[0;32mI (11713) UART READ: [LEN: ]: 1[0m
[0;32mI (11753) UART_READ: : ERROR[0m
0xFF
[0;32mI (11753) UART READ: [LEN: ]: 1[0m
[0;32mI (11813) UART_READ: : ERROR[0m
0xFE
[0;32mI (11813) UART READ: [LEN: ]: 1[0m
[0;32mI (11893) UART_READ: : ERROR[0m
0xFF
[0;32mI (11893) UART READ: [LEN: ]: 1[0m
[0;32mI (11933) UART_READ: : ERROR[0m
0xFD
[0;32mI (11933) UART READ: [LEN: ]: 1[0m
[0;32mI (11973) UART_READ: : ERROR[0m
0xFF
[0;32mI (11973) UART READ: [LEN: ]: 1[0m
[0;32mI (11993) UART_READ: : ERROR[0m
0xFD
[0;32mI (11993) UART READ: [LEN: ]: 1[0m
[0;32mI (12013) UART_READ: : ERROR[0m
0xFE
[0;32mI (12013) UART READ: [LEN: ]: 1[0m
[0;32mI (12073) UART_READ: : ERROR[0m
0xFF
[0;32mI (12073) UART READ: [LEN: ]: 1[0m
[0;32mI (12113) UART_READ: : ERROR[0m
0xFF
[0;32mI (12113) UART READ: [LEN: ]: 1[0m
[0;32mI (12133) UART_READ: : ERROR[0m
0xFC
[0;32mI (12133) UART READ: [LEN: ]: 1[0m
[0;32mI (12193) UART_READ: : ERROR[0m
0xFE
[0;32mI (12193) UART READ: [LEN: ]: 1[0m
[0;32mI (12233) UART_READ: : ERROR[0m
0xFD
[0;32mI (12233) UART READ: [LEN: ]: 1[0m
[0;32mI (12313) UART_READ: : ERROR[0m
0xFE
[0;32mI (12313) UART READ: [LEN: ]: 1[0m
[0;32mI (12333) UART_READ: : ERROR[0m
0xFF
[0;32mI (12333) UART READ: [LEN: ]: 1[0m
[0;32mI (12353) UART_READ: : ERROR[0m
0xFF
[0;32mI (12353) UART READ: [LEN: ]: 1[0m
[0;32mI (12413) UART_READ: : ERROR[0m
0xFF
[0;32mI (12413) UART READ: [LEN: ]: 1[0m
[0;32mI (12493) UART_READ: : ERROR[0m
0xFE
[0;32mI (12493) UART READ: [LEN: ]: 1[0m
[0;32mI (12573) UART_READ: : ERROR[0m
0xFF
[0;32mI (12573) UART READ: [LEN: ]: 1[0m
[0;32mI (12593) UART_READ: : ERROR[0m
0xFE
[0;32mI (12593) UART READ: [LEN: ]: 1[0m
[0;32mI (12653) UART_READ: : ERROR[0m
0xFF
[0;32mI (12653) UART READ: [LEN: ]: 1[0m
[0;32mI (12773) UART_READ: : ERROR[0m
0xFE
[0;32mI (12773) UART READ: [LEN: ]: 1[0m
[0;32mI (12813) UART_READ: : ERROR[0m
0xFC
[0;32mI (12813) UART READ: [LEN: ]: 1[0m
[0;32mI (12953) UART_READ: : ERROR[0m
0xFB
[0;32mI (12953) UART READ: [LEN: ]: 1[0m
[0;32mI (13013) UART_READ: : ERROR[0m
0xFE
[0;32mI (13013) UART READ: [LEN: ]: 1[0m
[0;32mI (13073) UART_READ: : ERROR[0m
0xFF
[0;32mI (13073) UART READ: [LEN: ]: 1[0m
[0;32mI (13113) UART_READ: : ERROR[0m
0xFF
[0;32mI (13113) UART READ: [LEN: ]: 1[0m
[0;32mI (13153) UART_READ: : ERROR[0m
0xFE
[0;32mI (13153) UART READ: [LEN: ]: 1[0m
[0;32mI (13273) UART_READ: : ERROR[0m
0xFC
[0;32mI (13273) UART READ: [LEN: ]: 1[0m
[0;32mI (13293) UART_READ: : ERROR[0m
0xFF
[0;32mI (13293) UART READ: [LEN: ]: 1[0m
[0;32mI (13393) UART_READ: : ERROR[0m
0xFF
[0;32mI (13393) UART READ: [LEN: ]: 1[0m
[0;32mI (13453) UART_READ: : ERROR[0m
0xFC
[0;32mI (13453) UART READ: [LEN: ]: 1[0m
[0;32mI (13513) UART_READ: : ERROR[0m
0xFE
[0;32mI (13513) UART READ: [LEN: ]: 1[0m
[0;32mI (13553) UART_READ: : ERROR[0m
0xFE
[0;32mI (13553) UART READ: [LEN: ]: 1[0m
[0;32mI (13573) UART_READ: : ERROR[0m
0xFE
[0;32mI (13573) UART READ: [LEN: ]: 1[0m
[0;32mI (13593) UART_READ: : ERROR[0m
0xFF
[0;32mI (13593) UART READ: [LEN: ]: 1[0m
[0;32mI (13613) UART_READ: : ERROR[0m
0xFF
[0;32mI (13613) UART READ: [LEN: ]: 1[0m
[0;32mI (13673) UART_READ: : ERROR[0m
0xFF
[0;32mI (13673) UART READ: [LEN: ]: 1[0m
[0;32mI (13713) UART_READ: : ERROR[0m
0xFC
[0;32mI (13713) UART READ: [LEN: ]: 1[0m
[0;32mI (13733) UART_READ: : ERROR[0m
0xFE
[0;32mI (13733) UART READ: [LEN: ]: 1[0m
[0;32mI (13773) UART_READ: : ERROR[0m
0xFC
[0;32mI (13773) UART READ: [LEN: ]: 1[0m
[0;32mI (13793) UART_READ: : ERROR[0m
0xFE
[0;32mI (13793) UART READ: [LEN: ]: 1[0m
[0;32mI (13853) UART_READ: : ERROR[0m
0xFE
[0;32mI (13853) UART READ: [LEN: ]: 1[0m
[0;32mI (13873) UART_READ: : ERROR[0m
0xFC
[0;32mI (13873) UART READ: [LEN: ]: 1[0m
[0;32mI (13953) UART_READ: : ERROR[0m
0xFC
[0;32mI (13953) UART READ: [LEN: ]: 1[0m
[0;32mI (14053) UART_READ: : ERROR[0m
0xFC
[0;32mI (14053) UART READ: [LEN: ]: 1[0m
[0;32mI (14073) UART_READ: : ERROR[0m
0xFE
[0;32mI (14073) UART READ: [LEN: ]: 1[0m
[0;32mI (14113) UART_READ: : ERROR[0m
0xFD
[0;32mI (14113) UART READ: [LEN: ]: 1[0m
[0;32mI (14173) UART_READ: : ERROR[0m
0xF9
[0;32mI (14173) UART READ: [LEN: ]: 1[0m
[0;32mI (14233) UART_READ: : ERROR[0m
0xF9
[0;32mI (14233) UART READ: [LEN: ]: 1[0m
[0;32mI (14273) UART_READ: : ERROR[0m
0xF8
[0;32mI (14273) UART READ: [LEN: ]: 1[0m
[0;32mI (14293) UART_READ: : ERROR[0m
0xFC
[0;32mI (14293) UART READ: [LEN: ]: 1[0m
[0;32mI (14353) UART_READ: : ERROR[0m
0xFE
[0;32mI (14353) UART READ: [LEN: ]: 1[0m
[0;32mI (14373) UART_READ: : ERROR[0m
0xFF
[0;32mI (14373) UART READ: [LEN: ]: 1[0m
[0;32mI (14413) UART_READ: : ERROR[0m
0xFE
[0;32mI (14413) UART READ: [LEN: ]: 1[0m
[0;32mI (14493) UART_READ: : ERROR[0m
0xFE
[0;32mI (14493) UART READ: [LEN: ]: 1[0m
[0;32mI (14533) UART_READ: : ERROR[0m
0xFC
[0;32mI (14533) UART READ: [LEN: ]: 1[0m
[0;32mI (14593) UART_READ: : ERROR[0m
0xFD
[0;32mI (14593) UART READ: [LEN: ]: 1[0m
[0;32mI (14613) UART_READ: : ERROR[0m
0xFB
[0;32mI (14613) UART READ: [LEN: ]: 1[0m
[0;32mI (14693) UART_READ: : ERROR[0m
0xF9
[0;32mI (14693) UART READ: [LEN: ]: 1[0m
[0;32mI (14713) UART_READ: : ERROR[0m
0xFE
[0;32mI (14713) UART READ: [LEN: ]: 1[0m
[0;32mI (14773) UART_READ: : ERROR[0m
0xFA
[0;32mI (14773) UART READ: [LEN: ]: 1[0m
[0;32mI (14813) UART_READ: : ERROR[0m
0xFF
[0;32mI (14813) UART READ: [LEN: ]: 1[0m
[0;32mI (14833) UART_READ: : ERROR[0m
0xFE
[0;32mI (14833) UART READ: [LEN: ]: 1[0m
[0;32mI (14873) UART_READ: : ERROR[0m
0xF8
[0;32mI (14873) UART READ: [LEN: ]: 1[0m
[0;32mI (14953) UART_READ: : ERROR[0m
0xF8
[0;32mI (14953) UART READ: [LEN: ]: 1[0m
[0;32mI (14993) UART_READ: : ERROR[0m
0xFE
[0;32mI (14993) UART READ: [LEN: ]: 1[0m
[0;32mI (15053) UART_READ: : ERROR[0m
0xFD
[0;32mI (15053) UART READ: [LEN: ]: 1[0m
[0;32mI (15113) UART_READ: : ERROR[0m
0xF9
[0;32mI (15113) UART READ: [LEN: ]: 1[0m
[0;32mI (15133) UART_READ: : ERROR[0m
0xFF
[0;32mI (15133) UART READ: [LEN: ]: 1[0m
[0;32mI (15173) UART_READ: : ERROR[0m
0xFE
[0;32mI (15173) UART READ: [LEN: ]: 1[0m
[0;32mI (15233) UART_READ: : ERROR[0m
0xFE
[0;32mI (15233) UART READ: [LEN: ]: 1[0m
[0;32mI (15253) UART_READ: : ERROR[0m
0xFE
[0;32mI (15253) UART READ: [LEN: ]: 1[0m
[0;32mI (15273) UART_READ: : ERROR[0m
0xFE
[0;32mI (15273) UART READ: [LEN: ]: 1[0m
[0;32mI (15373) UART_READ: : ERROR[0m
0xFE
[0;32mI (15373) UART READ: [LEN: ]: 1[0m
[0;32mI (15413) UART_READ: : ERROR[0m
0xF8
[0;32mI (15413) UART READ: [LEN: ]: 1[0m
[0;32mI (15453) UART_READ: : ERROR[0m
0xFE
[0;32mI (15453) UART READ: [LEN: ]: 1[0m
[0;32mI (15573) UART_READ: : ERROR[0m
0xFC
[0;32mI (15573) UART READ: [LEN: ]: 1[0m
[0;32mI (15713) UART_READ: : ERROR[0m
0xF9
[0;32mI (15713) UART READ: [LEN: ]: 1[0m
[0;32mI (15733) UART_READ: : ERROR[0m
0xFF
[0;32mI (15733) UART READ: [LEN: ]: 1[0m
[0;32mI (15813) UART_READ: : ERROR[0m
0xFE
[0;32mI (15813) UART READ: [LEN: ]: 1[0m
[0;32mI (15873) UART_READ: : ERROR[0m
0xFC
[0;32mI (15873) UART READ: [LEN: ]: 1[0m
[0;32mI (15893) UART_READ: : ERROR[0m
0xFF
[0;32mI (15893) UART READ: [LEN: ]: 1[0m
[0;32mI (15973) UART_READ: : ERROR[0m
0xFC
[0;32mI (15973) UART READ: [LEN: ]: 1[0m
[0;32mI (16013) UART_READ: : ERROR[0m
0xF9
[0;32mI (16013) UART READ: [LEN: ]: 1[0m
[0;32mI (16033) UART_READ: : ERROR[0m
0xFF
[0;32mI (16033) UART READ: [LEN: ]: 1[0m
[0;32mI (16073) UART_READ: : ERROR[0m
0xFB
[0;32mI (16073) UART READ: [LEN: ]: 1[0m
[0;32mI (16113) UART_READ: : ERROR[0m
0xF9
[0;32mI (16113) UART READ: [LEN: ]: 1[0m
[0;32mI (16173) UART_READ: : ERROR[0m
0xFC
[0;32mI (16173) UART READ: [LEN: ]: 1[0m
[0;32mI (16213) UART_READ: : ERROR[0m
0xFC
[0;32mI (16213) UART READ: [LEN: ]: 1[0m
[0;32mI (16273) UART_READ: : ERROR[0m
0xF9
[0;32mI (16273) UART READ: [LEN: ]: 1[0m
[0;32mI (16333) UART_READ: : ERROR[0m
0xFF
[0;32mI (16333) UART READ: [LEN: ]: 1[0m
[0;32mI (16353) UART_READ: : ERROR[0m
0xFF
[0;32mI (16353) UART READ: [LEN: ]: 1[0m
[0;32mI (16513) UART_READ: : ERROR[0m
0xFF
[0;32mI (16513) UART READ: [LEN: ]: 1[0m
[0;32mI (16553) UART_READ: : ERROR[0m
0xFE
[0;32mI (16553) UART READ: [LEN: ]: 1[0m
[0;32mI (16593) UART_READ: : ERROR[0m
0xFF
[0;32mI (16593) UART READ: [LEN: ]: 1[0m
[0;32mI (16613) UART_READ: : ERROR[0m
0xFE
[0;32mI (16613) UART READ: [LEN: ]: 1[0m
[0;32mI (16633) UART_READ: : ERROR[0m
0xF9
[0;32mI (16633) UART READ: [LEN: ]: 1[0m
[0;32mI (16693) UART_READ: : ERROR[0m
0xFE
[0;32mI (16693) UART READ: [LEN: ]: 1[0m
[0;32mI (16773) UART_READ: : ERROR[0m
0xFB
[0;32mI (16773) UART READ: [LEN: ]: 1[0m
[0;32mI (16793) UART_READ: : ERROR[0m
0xFC
[0;32mI (16793) UART READ: [LEN: ]: 1[0m
[0;32mI (16813) UART_READ: : ERROR[0m
0xF9
[0;32mI (16813) UART READ: [LEN: ]: 1[0m
[0;32mI (16893) UART_READ: : ERROR[0m
0xFC
[0;32mI (16893) UART READ: [LEN: ]: 1[0m
[0;32mI (16973) UART_READ: : ERROR[0m
0xFF
[0;32mI (16973) UART READ: [LEN: ]: 1[0m
[0;32mI (17033) UART_READ: : ERROR[0m
0xF9
[0;32mI (17033) UART READ: [LEN: ]: 1[0m
[0;32mI (17093) UART_READ: : ERROR[0m
0xFF
[0;32mI (17093) UART READ: [LEN: ]: 1[0m
[0;32mI (17113) UART_READ: : ERROR[0m
0xFC
[0;32mI (17113) UART READ: [LEN: ]: 1[0m
[0;32mI (17153) UART_READ: : ERROR[0m
0xFF
[0;32mI (17153) UART READ: [LEN: ]: 1[0m
[0;32mI (17213) UART_READ: : ERROR[0m
0xFF
[0;32mI (17213) UART READ: [LEN: ]: 1[0m
[0;32mI (17253) UART_READ: : ERROR[0m
0xFC
[0;32mI (17253) UART READ: [LEN: ]: 1[0m
[0;32mI (17313) UART_READ: : ERROR[0m
0xFC
[0;32mI (17313) UART READ: [LEN: ]: 1[0m
[0;32mI (17393) UART_READ: : ERROR[0m
0xFC
[0;32mI (17393) UART READ: [LEN: ]: 1[0m
[0;32mI (17473) UART_READ: : ERROR[0m
0xF8
[0;32mI (17473) UART READ: [LEN: ]: 1[0m
[0;32mI (17493) UART_READ: : ERROR[0m
0xFB
[0;32mI (17493) UART READ: [LEN: ]: 1[0m
[0;32mI (17553) UART_READ: : ERROR[0m
0xF8
[0;32mI (17553) UART READ: [LEN: ]: 1[0m
[0;32mI (17593) UART_READ: : ERROR[0m
0xFF
[0;32mI (17593) UART READ: [LEN: ]: 1[0m
[0;32mI (17613) UART_READ: : ERROR[0m
0xFF
[0;32mI (17613) UART READ: [LEN: ]: 1[0m
[0;32mI (17753) UART_READ: : ERROR[0m
0xFF
[0;32mI (17753) UART READ: [LEN: ]: 1[0m
[0;32mI (17853) UART_READ: : ERROR[0m
0xFD
[0;32mI (17853) UART READ: [LEN: ]: 1[0m
[0;32mI (17893) UART_READ: : ERROR[0m
0xFE
[0;32mI (17893) UART READ: [LEN: ]: 1[0m
[0;32mI (17933) UART_READ: : ERROR[0m
0xFC
[0;32mI (17933) UART READ: [LEN: ]: 1[0m
[0;32mI (17953) UART_READ: : ERROR[0m
0xFB
[0;32mI (17953) UART READ: [LEN: ]: 1[0m
[0;32mI (18153) UART_READ: : ERROR[0m
0xF9
[0;32mI (18153) UART READ: [LEN: ]: 1[0m
[0;32mI (18313) UART_READ: : ERROR[0m
0xFF
[0;32mI (18313) UART READ: [LEN: ]: 1[0m
[0;32mI (18353) UART_READ: : ERROR[0m
0xFC
[0;32mI (18353) UART READ: [LEN: ]: 1[0m
[0;32mI (18373) UART_READ: : ERROR[0m
0xFF
[0;32mI (18373) UART READ: [LEN: ]: 1[0m
[0;32mI (18473) UART_READ: : ERROR[0m
0xFF
[0;32mI (18473) UART READ: [LEN: ]: 1[0m
[0;32mI (18513) UART_READ: : ERROR[0m
0xFE
[0;32mI (18513) UART READ: [LEN: ]: 1[0m
[0;32mI (18593) UART_READ: : ERROR[0m
0xFF
[0;32mI (18593) UART READ: [LEN: ]: 1[0m
[0;32mI (18713) UART_READ: : ERROR[0m
0xFF
[0;32mI (18713) UART READ: [LEN: ]: 1[0m
[0;32mI (18773) UART_READ: : ERROR[0m
0xFE
[0;32mI (18773) UART READ: [LEN: ]: 1[0m
[0;32mI (18793) UART_READ: : ERROR[0m
0xF9
[0;32mI (18793) UART READ: [LEN: ]: 1[0m
[0;32mI (18813) UART_READ: : ERROR[0m
0xFF
[0;32mI (18813) UART READ: [LEN: ]: 1[0m
[0;32mI (18913) UART_READ: : ERROR[0m
0xFD
[0;32mI (18913) UART READ: [LEN: ]: 1[0m
[0;32mI (18993) UART_READ: : ERROR[0m
0xFC
[0;32mI (18993) UART READ: [LEN: ]: 1[0m
[0;32mI (19033) UART_READ: : ERROR[0m
0xFF
[0;32mI (19033) UART READ: [LEN: ]: 1[0m
[0;32mI (19093) UART_READ: : ERROR[0m
0xFF
[0;32mI (19093) UART READ: [LEN: ]: 1[0m
[0;32mI (19113) UART_READ: : ERROR[0m
0xFE
[0;32mI (19113) UART READ: [LEN: ]: 1[0m
[0;32mI (19153) UART_READ: : ERROR[0m
0xFD
[0;32mI (19153) UART READ: [LEN: ]: 1[0m
[0;32mI (19233) UART_READ: : ERROR[0m
0xFE
[0;32mI (19233) UART READ: [LEN: ]: 1[0m
[0;32mI (19273) UART_READ: : ERROR[0m
0xF8
[0;32mI (19273) UART READ: [LEN: ]: 1[0m
[0;32mI (19293) UART_READ: : ERROR[0m
0xFF
[0;32mI (19293) UART READ: [LEN: ]: 1[0m
[0;32mI (19353) UART_READ: : ERROR[0m
0xFF
[0;32mI (19353) UART READ: [LEN: ]: 1[0m
[0;32mI (19393) UART_READ: : ERROR[0m
0xFE
[0;32mI (19393) UART READ: [LEN: ]: 1[0m
[0;32mI (19413) UART_READ: : ERROR[0m
0xFF
[0;32mI (19413) UART READ: [LEN: ]: 1[0m
[0;32mI (19433) UART_READ: : ERROR[0m
0xFF
[0;32mI (19433) UART READ: [LEN: ]: 1[0m
[0;32mI (19473) UART_READ: : ERROR[0m
0xF8
[0;32mI (19473) UART READ: [LEN: ]: 1[0m
[0;32mI (19493) UART_READ: : ERROR[0m
0xFE
[0;32mI (19493) UART READ: [LEN: ]: 1[0m
[0;32mI (19513) UART_READ: : ERROR[0m
0xFF
[0;32mI (19513) UART READ: [LEN: ]: 1[0m
[0;32mI (19533) UART_READ: : ERROR[0m
0xFD
[0;32mI (19533) UART READ: [LEN: ]: 1[0m
[0;32mI (19613) UART_READ: : ERROR[0m
0xFF
[0;32mI (19613) UART READ: [LEN: ]: 1[0m
[0;32mI (19653) UART_READ: : ERROR[0m
0xFF
[0;32mI (19653) UART READ: [LEN: ]: 1[0m
[0;32mI (19753) UART_READ: : ERROR[0m
0xFF
[0;32mI (19753) UART READ: [LEN: ]: 1[0m
[0;32mI (19793) UART_READ: : ERROR[0m
0xFC
[0;32mI (19793) UART READ: [LEN: ]: 1[0m
[0;32mI (19813) UART_READ: : ERROR[0m
0xFF
[0;32mI (19813) UART READ: [LEN: ]: 1[0m
[0;32mI (19873) UART_READ: : ERROR[0m
0xFC
[0;32mI (19873) UART READ: [LEN: ]: 1[0m
[0;32mI (19933) UART_READ: : ERROR[0m
0xFE
[0;32mI (19933) UART READ: [LEN: ]: 1[0m
[0;32mI (19953) UART_READ: : ERROR[0m
0xFE
[0;32mI (19953) UART READ: [LEN: ]: 1[0m
[0;32mI (20013) UART_READ: : ERROR[0m
0xFE
[0;32mI (20013) UART READ: [LEN: ]: 1[0m
[0;32mI (20053) UART_READ: : ERROR[0m
0xFF
[0;32mI (20053) UART READ: [LEN: ]: 1[0m
[0;32mI (20073) UART_READ: : ERROR[0m
0xFC
[0;32mI (20073) UART READ: [LEN: ]: 1[0m
[0;32mI (20113) UART_READ: : ERROR[0m
0xFC
[0;32mI (20113) UART READ: [LEN: ]: 1[0m
[0;32mI (20153) UART_READ: : ERROR[0m
0xFF
[0;32mI (20153) UART READ: [LEN: ]: 1[0m
[0;32mI (20173) UART_READ: : ERROR[0m
0xFD
[0;32mI (20173) UART READ: [LEN: ]: 1[0m
[0;32mI (20213) UART_READ: : ERROR[0m
0xFF
[0;32mI (20213) UART READ: [LEN: ]: 1[0m
[0;32mI (20293) UART_READ: : ERROR[0m
0xFF
[0;32mI (20293) UART READ: [LEN: ]: 1[0m
[0;32mI (20313) UART_READ: : ERROR[0m
0xFF
[0;32mI (20313) UART READ: [LEN: ]: 1[0m
[0;32mI (20333) UART_READ: : ERROR[0m
0xFF
[0;32mI (20333) UART READ: [LEN: ]: 1[0m
[0;32mI (20373) UART_READ: : ERROR[0m
0xFF
[0;32mI (20373) UART READ: [LEN: ]: 1[0m
[0;32mI (20503) UART_READ: : ERROR[0m
0xFF
[0;32mI (20513) UART READ: [LEN: ]: 1[0m
[0;32mI (20523) UART_READ: : ERROR[0m
0xFF
[0;32mI (20533) UART READ: [LEN: ]: 1[0m
[0;32mI (20543) UART_READ: : ERROR[0m
0xFF
[0;32mI (20553) UART READ: [LEN: ]: 1[0m
[0;32mI (20563) UART_READ: : ERROR[0m
0xFE
[0;32mI (20573) UART READ: [LEN: ]: 1[0m
[0;32mI (20643) UART_READ: : ERROR[0m
0xFF
[0;32mI (20643) UART READ: [LEN: ]: 1[0m
[0;32mI (20663) UART_READ: : ERROR[0m
0xF9
[0;32mI (20663) UART READ: [LEN: ]: 1[0m
[0;32mI (20703) UART_READ: : ERROR[0m
0xFF
[0;32mI (20703) UART READ: [LEN: ]: 1[0m
[0;32mI (20723) UART_READ: : ERROR[0m
0xFA
[0;32mI (20723) UART READ: [LEN: ]: 1[0m
[0;32mI (20783) UART_READ: : ERROR[0m
0xFE
[0;32mI (20783) UART READ: [LEN: ]: 1[0m
[0;32mI (20843) UART_READ: : ERROR[0m
0xFF
[0;32mI (20843) UART READ: [LEN: ]: 1[0m
[0;32mI (20883) UART_READ: : ERROR[0m
0xFF
[0;32mI (20883) UART READ: [LEN: ]: 1[0m
[0;32mI (20923) UART_READ: : ERROR[0m
0xFF
[0;32mI (20923) UART READ: [LEN: ]: 1[0m
[0;32mI (20943) UART_READ: : ERROR[0m
0xFE
[0;32mI (20943) UART READ: [LEN: ]: 1[0m
[0;32mI (21003) UART_READ: : ERROR[0m
0xFC
[0;32mI (21003) UART READ: [LEN: ]: 1[0m
[0;32mI (21083) UART_READ: : ERROR[0m
0xFE
[0;32mI (21083) UART READ: [LEN: ]: 1[0m
[0;32mI (21103) UART_READ: : ERROR[0m
0xFE
[0;32mI (21103) UART READ: [LEN: ]: 1[0m
[0;32mI (21163) UART_READ: : ERROR[0m
0xFC
[0;32mI (21163) UART READ: [LEN: ]: 1[0m
[0;32mI (21183) UART_READ: : ERROR[0m
0xFF
[0;32mI (21183) UART READ: [LEN: ]: 1[0m
[0;32mI (21243) UART_READ: : ERROR[0m
0xFF
[0;32mI (21243) UART READ: [LEN: ]: 1[0m
[0;32mI (21323) UART_READ: : ERROR[0m
0xFE
[0;32mI (21323) UART READ: [LEN: ]: 1[0m
[0;32mI (21343) UART_READ: : ERROR[0m
0xFF
[0;32mI (21343) UART READ: [LEN: ]: 1[0m
[0;32mI (21383) UART_READ: : ERROR[0m
0xFE
[0;32mI (21383) UART READ: [LEN: ]: 1[0m
[0;32mI (21403) UART_READ: : ERROR[0m
0xFE
[0;32mI (21403) UART READ: [LEN: ]: 1[0m
[0;32mI (21443) UART_READ: : ERROR[0m
0xFE
[0;32mI (21443) UART READ: [LEN: ]: 1[0m
[0;32mI (21463) UART_READ: : ERROR[0m
0xFF
[0;32mI (21463) UART READ: [LEN: ]: 1[0m
[0;32mI (21503) UART_READ: : ERROR[0m
0xFE
[0;32mI (21503) UART READ: [LEN: ]: 1[0m
[0;32mI (21523) UART_READ: : ERROR[0m
0xFD
[0;32mI (21523) UART READ: [LEN: ]: 1[0m
[0;32mI (21623) UART_READ: : ERROR[0m
0xFF
[0;32mI (21623) UART READ: [LEN: ]: 1[0m
[0;32mI (21643) UART_READ: : ERROR[0m
0xFE
[0;32mI (21643) UART READ: [LEN: ]: 1[0m
[0;32mI (21703) UART_READ: : ERROR[0m
0xFE
[0;32mI (21703) UART READ: [LEN: ]: 1[0m
[0;32mI (21723) UART_READ: : ERROR[0m
0xFF
[0;32mI (21723) UART READ: [LEN: ]: 1[0m
[0;32mI (21763) UART_READ: : ERROR[0m
0xFF
[0;32mI (21763) UART READ: [LEN: ]: 1[0m
[0;32mI (21783) UART_READ: : ERROR[0m
0xFF
[0;32mI (21783) UART READ: [LEN: ]: 1[0m
[0;32mI (21803) UART_READ: : ERROR[0m
0xFF
[0;32mI (21803) UART READ: [LEN: ]: 1[0m
[0;32mI (21863) UART_READ: : ERROR[0m
0xFE
[0;32mI (21863) UART READ: [LEN: ]: 1[0m
[0;32mI (21883) UART_READ: : ERROR[0m
0xFE
[0;32mI (21883) UART READ: [LEN: ]: 1[0m
[0;32mI (21943) UART_READ: : ERROR[0m
0xFE
[0;32mI (21943) UART READ: [LEN: ]: 1[0m
[0;32mI (21963) UART_READ: : ERROR[0m
0xFE
[0;32mI (21963) UART READ: [LEN: ]: 1[0m
[0;32mI (21983) UART_READ: : ERROR[0m
0xFF
[0;32mI (21983) UART READ: [LEN: ]: 1[0m
[0;32mI (22003) UART_READ: : ERROR[0m
0xFF
[0;32mI (22003) UART READ: [LEN: ]: 1[0m
[0;32mI (22023) UART_READ: : ERROR[0m
0xFE
[0;32mI (22023) UART READ: [LEN: ]: 1[0m
[0;32mI (22063) UART_READ: : ERROR[0m
0xFF
[0;32mI (22063) UART READ: [LEN: ]: 1[0m
[0;32mI (22083) UART_READ: : ERROR[0m
0xFF
[0;32mI (22083) UART READ: [LEN: ]: 1[0m
[0;32mI (22103) UART_READ: : ERROR[0m
0xFF
[0;32mI (22103) UART READ: [LEN: ]: 1[0m
[0;32mI (22143) UART_READ: : ERROR[0m
0xFB
[0;32mI (22143) UART READ: [LEN: ]: 1[0m
[0;32mI (22183) UART_READ: : ERROR[0m
0xFF
[0;32mI (22183) UART READ: [LEN: ]: 1[0m
[0;32mI (22203) UART_READ: : ERROR[0m
0xFE
[0;32mI (22203) UART READ: [LEN: ]: 1[0m
[0;32mI (22223) UART_READ: : ERROR[0m
0xFB
[0;32mI (22223) UART READ: [LEN: ]: 1[0m
[0;32mI (22283) UART_READ: : ERROR[0m
0xFF
[0;32mI (22283) UART READ: [LEN: ]: 1[0m
[0;32mI (22323) UART_READ: : ERROR[0m
0xFE
[0;32mI (22323) UART READ: [LEN: ]: 1[0m
[0;32mI (22343) UART_READ: : ERROR[0m
0xFE
[0;32mI (22343) UART READ: [LEN: ]: 1[0m
[0;32mI (22403) UART_READ: : ERROR[0m
0xFF
[0;32mI (22403) UART READ: [LEN: ]: 1[0m
[0;32mI (22443) UART_READ: : ERROR[0m
0xFF
[0;32mI (22443) UART READ: [LEN: ]: 1[0m
[0;32mI (22483) UART_READ: : ERROR[0m
0xFC
[0;32mI (22483) UART READ: [LEN: ]: 1[0m
[0;32mI (22503) UART_READ: : ERROR[0m
0xFE
[0;32mI (22503) UART READ: [LEN: ]: 1[0m
[0;32mI (22523) UART_READ: : ERROR[0m
0xFF
[0;32mI (22523) UART READ: [LEN: ]: 1[0m
[0;32mI (22583) UART_READ: : ERROR[0m
0xFE
[0;32mI (22583) UART READ: [LEN: ]: 1[0m
[0;32mI (22663) UART_READ: : ERROR[0m
0xFF
[0;32mI (22663) UART READ: [LEN: ]: 1[0m
[0;32mI (22683) UART_READ: : ERROR[0m
0xFE
[0;32mI (22683) UART READ: [LEN: ]: 1[0m
[0;32mI (22703) UART_READ: : ERROR[0m
0xFC
[0;32mI (22703) UART READ: [LEN: ]: 1[0m
[0;32mI (22903) UART_READ: : ERROR[0m
0xFE
[0;32mI (22903) UART READ: [LEN: ]: 1[0m
[0;32mI (22943) UART_READ: : ERROR[0m
0xFC
[0;32mI (22943) UART READ: [LEN: ]: 1[0m
[0;32mI (22963) UART_READ: : ERROR[0m
0xFF
[0;32mI (22963) UART READ: [LEN: ]: 1[0m
[0;32mI (22983) UART_READ: : ERROR[0m
0xFF
[0;32mI (22983) UART READ: [LEN: ]: 1[0m
[0;32mI (23003) UART_READ: : ERROR[0m
0xFF
[0;32mI (23003) UART READ: [LEN: ]: 1[0m
[0;32mI (23083) UART_READ: : ERROR[0m
0xFF
[0;32mI (23083) UART READ: [LEN: ]: 1[0m
[0;32mI (23183) UART_READ: : ERROR[0m
0xFE
[0;32mI (23183) UART READ: [LEN: ]: 1[0m
[0;32mI (23203) UART_READ: : ERROR[0m
0xFF
[0;32mI (23203) UART READ: [LEN: ]: 1[0m
[0;32mI (23283) UART_READ: : ERROR[0m
0xFD
[0;32mI (23283) UART READ: [LEN: ]: 1[0m
[0;32mI (23363) UART_READ: : ERROR[0m
0xFC
[0;32mI (23363) UART READ: [LEN: ]: 1[0m
[0;32mI (23383) UART_READ: : ERROR[0m
0xFE
[0;32mI (23383) UART READ: [LEN: ]: 1[0m
[0;32mI (23463) UART_READ: : ERROR[0m
0xFF
[0;32mI (23463) UART READ: [LEN: ]: 1[0m
[0;32mI (23483) UART_READ: : ERROR[0m
0xFF
[0;32mI (23483) UART READ: [LEN: ]: 1[0m
[0;32mI (23523) UART_READ: : ERROR[0m
0xFF
[0;32mI (23523) UART READ: [LEN: ]: 1[0m
[0;32mI (23543) UART_READ: : ERROR[0m
0xFE
[0;32mI (23543) UART READ: [LEN: ]: 1[0m
[0;32mI (23603) UART_READ: : ERROR[0m
0xFF
[0;32mI (23603) UART READ: [LEN: ]: 1[0m
[0;32mI (23623) UART_READ: : ERROR[0m
0xFE
[0;32mI (23623) UART READ: [LEN: ]: 1[0m
[0;32mI (23663) UART_READ: : ERROR[0m
0xFE
[0;32mI (23663) UART READ: [LEN: ]: 1[0m
[0;32mI (23683) UART_READ: : ERROR[0m
0xFF
[0;32mI (23683) UART READ: [LEN: ]: 1[0m
[0;32mI (23703) UART_READ: : ERROR[0m
0xFC
[0;32mI (23703) UART READ: [LEN: ]: 1[0m
[0;32mI (23723) UART_READ: : ERROR[0m
0xFE
[0;32mI (23723) UART READ: [LEN: ]: 1[0m
[0;32mI (23763) UART_READ: : ERROR[0m
0xFE
[0;32mI (23763) UART READ: [LEN: ]: 1[0m
[0;32mI (23783) UART_READ: : ERROR[0m
0xFF
[0;32mI (23783) UART READ: [LEN: ]: 1[0m
[0;32mI (23863) UART_READ: : ERROR[0m
0xFF
[0;32mI (23863) UART READ: [LEN: ]: 1[0m
[0;32mI (23903) UART_READ: : ERROR[0m
0xFE
[0;32mI (23903) UART READ: [LEN: ]: 1[0m
[0;32mI (23923) UART_READ: : ERROR[0m
0xFE
[0;32mI (23923) UART READ: [LEN: ]: 1[0m
[0;32mI (24023) UART_READ: : ERROR[0m
0xFE
[0;32mI (24023) UART READ: [LEN: ]: 1[0m
[0;32mI (24043) UART_READ: : ERROR[0m
0xFF
[0;32mI (24043) UART READ: [LEN: ]: 1[0m
[0;32mI (24083) UART_READ: : ERROR[0m
0xFC
[0;32mI (24083) UART READ: [LEN: ]: 1[0m
[0;32mI (24103) UART_READ: : ERROR[0m
0xF9
[0;32mI (24103) UART READ: [LEN: ]: 1[0m
[0;32mI (24163) UART_READ: : ERROR[0m
0xFF
[0;32mI (24163) UART READ: [LEN: ]: 1[0m
[0;32mI (24203) UART_READ: : ERROR[0m
0xFE
[0;32mI (24203) UART READ: [LEN: ]: 1[0m
[0;32mI (24243) UART_READ: : ERROR[0m
0xF8
[0;32mI (24243) UART READ: [LEN: ]: 1[0m
[0;32mI (24263) UART_READ: : ERROR[0m
0xFE
[0;32mI (24263) UART READ: [LEN: ]: 1[0m
[0;32mI (24283) UART_READ: : ERROR[0m
0xFF
[0;32mI (24283) UART READ: [LEN: ]: 1[0m
[0;32mI (24343) UART_READ: : ERROR[0m
0xFE
[0;32mI (24343) UART READ: [LEN: ]: 1[0m
[0;32mI (24363) UART_READ: : ERROR[0m
0xFF
[0;32mI (24363) UART READ: [LEN: ]: 1[0m
[0;32mI (24403) UART_READ: : ERROR[0m
0xFC
[0;32mI (24403) UART READ: [LEN: ]: 1[0m
[0;32mI (24443) UART_READ: : ERROR[0m
0xFE
[0;32mI (24443) UART READ: [LEN: ]: 1[0m
[0;32mI (24563) UART_READ: : ERROR[0m
0xFF
[0;32mI (24563) UART READ: [LEN: ]: 1[0m
[0;32mI (24603) UART_READ: : ERROR[0m
0xFE
[0;32mI (24603) UART READ: [LEN: ]: 1[0m
[0;32mI (24623) UART_READ: : ERROR[0m
0xFB
[0;32mI (24623) UART READ: [LEN: ]: 1[0m
[0;32mI (24703) UART_READ: : ERROR[0m
0xFC
[0;32mI (24703) UART READ: [LEN: ]: 1[0m
[0;32mI (24783) UART_READ: : ERROR[0m
0xFD
[0;32mI (24783) UART READ: [LEN: ]: 1[0m
[0;32mI (24823) UART_READ: : ERROR[0m
0xFE
[0;32mI (24823) UART READ: [LEN: ]: 1[0m
[0;32mI (24883) UART_READ: : ERROR[0m
0xFE
[0;32mI (24883) UART READ: [LEN: ]: 1[0m
[0;32mI (24923) UART_READ: : ERROR[0m
0xFF
[0;32mI (24923) UART READ: [LEN: ]: 1[0m
[0;32mI (24943) UART_READ: : ERROR[0m
0xFE
[0;32mI (24943) UART READ: [LEN: ]: 1[0m
[0;32mI (24963) UART_READ: : ERROR[0m
0xFE
[0;32mI (24963) UART READ: [LEN: ]: 1[0m
[0;32mI (24983) UART_READ: : ERROR[0m
0xFF
[0;32mI (24983) UART READ: [LEN: ]: 1[0m
[0;32mI (25043) UART_READ: : ERROR[0m
0xFF
[0;32mI (25043) UART READ: [LEN: ]: 1[0m
[0;32mI (25083) UART_READ: : ERROR[0m
0xFD
[0;32mI (25083) UART READ: [LEN: ]: 1[0m
[0;32mI (25103) UART_READ: : ERROR[0m
0xFE
[0;32mI (25103) UART READ: [LEN: ]: 1[0m
[0;32mI (25123) UART_READ: : ERROR[0m
0xFF
[0;32mI (25123) UART READ: [LEN: ]: 1[0m
[0;32mI (25183) UART_READ: : ERROR[0m
0xFF
[0;32mI (25183) UART READ: [LEN: ]: 1[0m
[0;32mI (25203) UART_READ: : ERROR[0m
0xFD
[0;32mI (25203) UART READ: [LEN: ]: 1[0m
[0;32mI (25323) UART_READ: : ERROR[0m
0xFF
[0;32mI (25323) UART READ: [LEN: ]: 1[0m
[0;32mI (25343) UART_READ: : ERROR[0m
0xFF
[0;32mI (25343) UART READ: [LEN: ]: 1[0m
[0;32mI (25363) UART_READ: : ERROR[0m
0xFF
[0;32mI (25363) UART READ: [LEN: ]: 1[0m
[0;32mI (25443) UART_READ: : ERROR[0m
0xFF
[0;32mI (25443) UART READ: [LEN: ]: 1[0m
[0;32mI (25503) UART_READ: : ERROR[0m
0xFE
[0;32mI (25503) UART READ: [LEN: ]: 1[0m
[0;32mI (25543) UART_READ: : ERROR[0m
0xFF
[0;32mI (25543) UART READ: [LEN: ]: 1[0m
[0;32mI (25563) UART_READ: : ERROR[0m
0xFF
[0;32mI (25563) UART READ: [LEN: ]: 1[0m
[0;32mI (25583) UART_READ: : ERROR[0m
0xFD
[0;32mI (25583) UART READ: [LEN: ]: 1[0m
[0;32mI (25623) UART_READ: : ERROR[0m
0xFF
[0;32mI (25623) UART READ: [LEN: ]: 1[0m
[0;32mI (25643) UART_READ: : ERROR[0m
0xFE
[0;32mI (25643) UART READ: [LEN: ]: 1[0m
[0;32mI (25663) UART_READ: : ERROR[0m
0xFE
[0;32mI (25663) UART READ: [LEN: ]: 1[0m
[0;32mI (25723) UART_READ: : ERROR[0m
0xFD
[0;32mI (25723) UART READ: [LEN: ]: 1[0m
[0;32mI (25763) UART_READ: : ERROR[0m
0xFD
[0;32mI (25763) UART READ: [LEN: ]: 1[0m
[0;32mI (25783) UART_READ: : ERROR[0m
0xFE
[0;32mI (25783) UART READ: [LEN: ]: 1[0m
[0;32mI (25803) UART_READ: : ERROR[0m
0xFF
[0;32mI (25803) UART READ: [LEN: ]: 1[0m
[0;32mI (25863) UART_READ: : ERROR[0m
0xFF
[0;32mI (25863) UART READ: [LEN: ]: 1[0m
[0;32mI (25883) UART_READ: : ERROR[0m
0xFE
[0;32mI (25883) UART READ: [LEN: ]: 1[0m
[0;32mI (25903) UART_READ: : ERROR[0m
0xFF
[0;32mI (25903) UART READ: [LEN: ]: 1[0m
[0;32mI (25923) UART_READ: : ERROR[0m
0xFF
[0;32mI (25923) UART READ: [LEN: ]: 1[0m
[0;32mI (25943) UART_READ: : ERROR[0m
0xFF
[0;32mI (25943) UART READ: [LEN: ]: 1[0m
[0;32mI (26023) UART_READ: : ERROR[0m
0xFD
[0;32mI (26023) UART READ: [LEN: ]: 1[0m
[0;32mI (26043) UART_READ: : ERROR[0m
0xFD
[0;32mI (26043) UART READ: [LEN: ]: 1[0m
[0;32mI (26083) UART_READ: : ERROR[0m
0xFF
[0;32mI (26083) UART READ: [LEN: ]: 1[0m
[0;32mI (26143) UART_READ: : ERROR[0m
0xFF
[0;32mI (26143) UART READ: [LEN: ]: 1[0m
[0;32mI (26223) UART_READ: : ERROR[0m
0xFE
[0;32mI (26223) UART READ: [LEN: ]: 1[0m
[0;32mI (26243) UART_READ: : ERROR[0m
0xFF
[0;32mI (26243) UART READ: [LEN: ]: 1[0m
[0;32mI (26303) UART_READ: : ERROR[0m
0xFF
[0;32mI (26303) UART READ: [LEN: ]: 1[0m
[0;32mI (26323) UART_READ: : ERROR[0m
0xFE
[0;32mI (26323) UART READ: [LEN: ]: 1[0m
[0;32mI (26343) UART_READ: : ERROR[0m
0xFF
[0;32mI (26343) UART READ: [LEN: ]: 1[0m
[0;32mI (26423) UART_READ: : ERROR[0m
0xFF
[0;32mI (26423) UART READ: [LEN: ]: 1[0m
[0;32mI (26443) UART_READ: : ERROR[0m
0xFE
[0;32mI (26443) UART READ: [LEN: ]: 1[0m
[0;32mI (26463) UART_READ: : ERROR[0m
0xFF
[0;32mI (26463) UART READ: [LEN: ]: 1[0m
[0;32mI (26483) UART_READ: : ERROR[0m
0xFF
[0;32mI (26483) UART READ: [LEN: ]: 1[0m
[0;32mI (26543) UART_READ: : ERROR[0m
0xFE
[0;32mI (26543) UART READ: [LEN: ]: 1[0m
[0;32mI (26583) UART_READ: : ERROR[0m
0xFF
[0;32mI (26583) UART READ: [LEN: ]: 1[0m
[0;32mI (26603) UART_READ: : ERROR[0m
0xFF
[0;32mI (26603) UART READ: [LEN: ]: 1[0m
[0;32mI (26623) UART_READ: : ERROR[0m
0xFF
[0;32mI (26623) UART READ: [LEN: ]: 1[0m
[0;32mI (26703) UART_READ: : ERROR[0m
0xFF
[0;32mI (26703) UART READ: [LEN: ]: 1[0m
[0;32mI (26743) UART_READ: : ERROR[0m
0xFF
[0;32mI (26743) UART READ: [LEN: ]: 1[0m
[0;32mI (26783) UART_READ: : ERROR[0m
0xFF
[0;32mI (26783) UART READ: [LEN: ]: 1[0m
[0;32mI (26803) UART_READ: : ERROR[0m
0xFF
[0;32mI (26803) UART READ: [LEN: ]: 1[0m
[0;32mI (26823) UART_READ: : ERROR[0m
0xFF
[0;32mI (26823) UART READ: [LEN: ]: 1[0m
[0;32mI (26843) UART_READ: : ERROR[0m
0xFE
[0;32mI (26843) UART READ: [LEN: ]: 1[0m
[0;32mI (26963) UART_READ: : ERROR[0m
0xFF
[0;32mI (26963) UART READ: [LEN: ]: 1[0m
[0;32mI (26983) UART_READ: : ERROR[0m
0xFF
[0;32mI (26983) UART READ: [LEN: ]: 1[0m
[0;32mI (27023) UART_READ: : ERROR[0m
0xFF
[0;32mI (27023) UART READ: [LEN: ]: 1[0m
[0;32mI (27083) UART_READ: : ERROR[0m
0xFF
[0;32mI (27083) UART READ: [LEN: ]: 1[0m
[0;32mI (27183) UART_READ: : ERROR[0m
0xFF
[0;32mI (27183) UART READ: [LEN: ]: 1[0m
[0;32mI (27263) UART_READ: : ERROR[0m
0xFF
[0;32mI (27263) UART READ: [LEN: ]: 1[0m
[0;32mI (27323) UART_READ: : ERROR[0m
0xFF
[0;32mI (27323) UART READ: [LEN: ]: 1[0m
[0;32mI (27343) UART_READ: : ERROR[0m
0xFF
[0;32mI (27343) UART READ: [LEN: ]: 1[0m
[0;32mI (27403) UART_READ: : ERROR[0m
0xFF
[0;32mI (27403) UART READ: [LEN: ]: 1[0m
[0;32mI (27463) UART_READ: : ERROR[0m
0xFF
[0;32mI (27463) UART READ: [LEN: ]: 1[0m
[0;32mI (27483) UART_READ: : ERROR[0m
0xFF
[0;32mI (27483) UART READ: [LEN: ]: 1[0m
[0;32mI (27503) UART_READ: : ERROR[0m
0xFF
[0;32mI (27503) UART READ: [LEN: ]: 1[0m
[0;32mI (27523) UART_READ: : ERROR[0m
0xFF
[0;32mI (27523) UART READ: [LEN: ]: 1[0m
[0;32mI (27543) UART_READ: : ERROR[0m
0xFF
[0;32mI (27543) UART READ: [LEN: ]: 1[0m
[0;32mI (27603) UART_READ: : ERROR[0m
0xFF
[0;32mI (27603) UART READ: [LEN: ]: 1[0m
[0;32mI (27663) UART_READ: : ERROR[0m
0xFF
[0;32mI (27663) UART READ: [LEN: ]: 1[0m
[0;32mI (27683) UART_READ: : ERROR[0m
0xFF
[0;32mI (27683) UART READ: [LEN: ]: 1[0m
[0;32mI (27743) UART_READ: : ERROR[0m
0xFF
[0;32mI (27743) UART READ: [LEN: ]: 1[0m
[0;32mI (27763) UART_READ: : ERROR[0m
0xFF
[0;32mI (27763) UART READ: [LEN: ]: 1[0m
[0;32mI (27783) UART_READ: : ERROR[0m
0xFF
[0;32mI (27783) UART READ: [LEN: ]: 1[0m
[0;32mI (27823) UART_READ: : ERROR[0m
0xFF
[0;32mI (27823) UART READ: [LEN: ]: 1[0m
[0;32mI (27843) UART_READ: : ERROR[0m
0xFF
[0;32mI (27843) UART READ: [LEN: ]: 1[0m
[0;32mI (27863) UART_READ: : ERROR[0m
0xFF
[0;32mI (27863) UART READ: [LEN: ]: 1[0m
[0;32mI (27883) UART_READ: : ERROR[0m
0xFF
[0;32mI (27883) UART READ: [LEN: ]: 1[0m
[0;32mI (27903) UART_READ: : ERROR[0m
0xFF
[0;32mI (27903) UART READ: [LEN: ]: 1[0m
[0;32mI (27943) UART_READ: : ERROR[0m
0xFF
[0;32mI (27943) UART READ: [LEN: ]: 1[0m
[0;32mI (27963) UART_READ: : ERROR[0m
0xFE
[0;32mI (27963) UART READ: [LEN: ]: 1[0m
[0;32mI (28003) UART_READ: : ERROR[0m
0xFF
[0;32mI (28003) UART READ: [LEN: ]: 1[0m
[0;32mI (28023) UART_READ: : ERROR[0m
0xFF
[0;32mI (28023) UART READ: [LEN: ]: 1[0m
[0;32mI (28043) UART_READ: : ERROR[0m
0xFE
[0;32mI (28043) UART READ: [LEN: ]: 1[0m
[0;32mI (28083) UART_READ: : ERROR[0m
0xFF
[0;32mI (28083) UART READ: [LEN: ]: 1[0m
[0;32mI (28123) UART_READ: : ERROR[0m
0xFF
[0;32mI (28123) UART READ: [LEN: ]: 1[0m
[0;32mI (28143) UART_READ: : ERROR[0m
0xFE
[0;32mI (28143) UART READ: [LEN: ]: 1[0m
[0;32mI (28163) UART_READ: : ERROR[0m
0xFF
[0;32mI (28163) UART READ: [LEN: ]: 1[0m
[0;32mI (28203) UART_READ: : ERROR[0m
0xFF
[0;32mI (28203) UART READ: [LEN: ]: 1[0m
[0;32mI (28223) UART_READ: : ERROR[0m
0xFF
[0;32mI (28223) UART READ: [LEN: ]: 1[0m
[0;32mI (28243) UART_READ: : ERROR[0m
0xFF
[0;32mI (28243) UART READ: [LEN: ]: 1[0m
[0;32mI (28263) UART_READ: : ERROR[0m
0xFF
[0;32mI (28263) UART READ: [LEN: ]: 1[0m
[0;32mI (28283) UART_READ: : ERROR[0m
0xFF
[0;32mI (28283) UART READ: [LEN: ]: 1[0m
[0;32mI (28303) UART_READ: : ERROR[0m
0xFF
[0;32mI (28303) UART READ: [LEN: ]: 1[0m
[0;32mI (28343) UART_READ: : ERROR[0m
0xFF
[0;32mI (28343) UART READ: [LEN: ]: 1[0m
[0;32mI (28363) UART_READ: : ERROR[0m
0xFF
[0;32mI (28363) UART READ: [LEN: ]: 1[0m
[0;32mI (28383) UART_READ: : ERROR[0m
0xFF
[0;32mI (28383) UART READ: [LEN: ]: 1[0m
[0;32mI (28403) UART_READ: : ERROR[0m
0xFF
[0;32mI (28403) UART READ: [LEN: ]: 1[0m
[0;32mI (28443) UART_READ: : ERROR[0m
0xFF
[0;32mI (28443) UART READ: [LEN: ]: 1[0m
[0;32mI (28463) UART_READ: : ERROR[0m
0xFF
[0;32mI (28463) UART READ: [LEN: ]: 1[0m
[0;32mI (28483) UART_READ: : ERROR[0m
0xFF
[0;32mI (28483) UART READ: [LEN: ]: 1[0m
[0;32mI (28523) UART_READ: : ERROR[0m
0xFF
[0;32mI (28523) UART READ: [LEN: ]: 1[0m
[0;32mI (28543) UART_READ: : ERROR[0m
0xFF
[0;32mI (28543) UART READ: [LEN: ]: 1[0m
[0;32mI (28563) UART_READ: : ERROR[0m
0xFF
[0;32mI (28563) UART READ: [LEN: ]: 1[0m
[0;32mI (28623) UART_READ: : ERROR[0m
0xFF
[0;32mI (28623) UART READ: [LEN: ]: 1[0m
[0;32mI (28683) UART_READ: : ERROR[0m
0xFF
[0;32mI (28683) UART READ: [LEN: ]: 1[0m
[0;32mI (28723) UART_READ: : ERROR[0m
0xFF
[0;32mI (28723) UART READ: [LEN: ]: 1[0m
[0;32mI (28783) UART_READ: : ERROR[0m
0xFF
[0;32mI (28783) UART READ: [LEN: ]: 1[0m
[0;32mI (28803) UART_READ: : ERROR[0m
0xFF
[0;32mI (28803) UART READ: [LEN: ]: 1[0m
[0;32mI (28823) UART_READ: : ERROR[0m
0xFF
[0;32mI (28823) UART READ: [LEN: ]: 1[0m
[0;32mI (28863) UART_READ: : ERROR[0m
0xFF
[0;32mI (28863) UART READ: [LEN: ]: 1[0m
[0;32mI (28883) UART_READ: : ERROR[0m
0xFE
[0;32mI (28883) UART READ: [LEN: ]: 1[0m
[0;32mI (28923) UART_READ: : ERROR[0m
0xFF
[0;32mI (28923) UART READ: [LEN: ]: 1[0m
[0;32mI (28983) UART_READ: : ERROR[0m
0xFF
[0;32mI (28983) UART READ: [LEN: ]: 1[0m
[0;32mI (29003) UART_READ: : ERROR[0m
0xFF
[0;32mI (29003) UART READ: [LEN: ]: 1[0m
[0;32mI (29043) UART_READ: : ERROR[0m
0xFF
[0;32mI (29043) UART READ: [LEN: ]: 1[0m
[0;32mI (29083) UART_READ: : ERROR[0m
0xFF
[0;32mI (29083) UART READ: [LEN: ]: 1[0m
[0;32mI (29103) UART_READ: : ERROR[0m
0xFF
[0;32mI (29103) UART READ: [LEN: ]: 1[0m
[0;32mI (29123) UART_READ: : ERROR[0m
0xFF
[0;32mI (29123) UART READ: [LEN: ]: 1[0m
[0;32mI (29183) UART_READ: : ERROR[0m
0xFF
[0;32mI (29183) UART READ: [LEN: ]: 1[0m
[0;32mI (29243) UART_READ: : ERROR[0m
0xFF
[0;32mI (29243) UART READ: [LEN: ]: 1[0m
[0;32mI (29263) UART_READ: : ERROR[0m
0xFF
[0;32mI (29263) UART READ: [LEN: ]: 1[0m
[0;32mI (29283) UART_READ: : ERROR[0m
0xFD
[0;32mI (29283) UART READ: [LEN: ]: 1[0m
[0;32mI (29303) UART_READ: : ERROR[0m
0xFF
[0;32mI (29303) UART READ: [LEN: ]: 1[0m
[0;32mI (29323) UART_READ: : ERROR[0m
0xFF
[0;32mI (29323) UART READ: [LEN: ]: 1[0m
[0;32mI (29363) UART_READ: : ERROR[0m
0xFF
[0;32mI (29363) UART READ: [LEN: ]: 1[0m
[0;32mI (29403) UART_READ: : ERROR[0m
0xFF
[0;32mI (29403) UART READ: [LEN: ]: 1[0m
[0;32mI (29503) UART_READ: : ERROR[0m
0xFF
[0;32mI (29503) UART READ: [LEN: ]: 1[0m
[0;32mI (29523) UART_READ: : ERROR[0m
0xFD
[0;32mI (29523) UART READ: [LEN: ]: 1[0m
[0;32mI (29563) UART_READ: : ERROR[0m
0xFF
[0;32mI (29563) UART READ: [LEN: ]: 1[0m
[0;32mI (29583) UART_READ: : ERROR[0m
0xFF
[0;32mI (29583) UART READ: [LEN: ]: 1[0m
[0;32mI (29603) UART_READ: : ERROR[0m
0xFF
[0;32mI (29603) UART READ: [LEN: ]: 1[0m
[0;32mI (29683) UART_READ: : ERROR[0m
0xFF
[0;32mI (29683) UART READ: [LEN: ]: 1[0m
[0;32mI (29723) UART_READ: : ERROR[0m
0xFF
[0;32mI (29723) UART READ: [LEN: ]: 1[0m
[0;32mI (29763) UART_READ: : ERROR[0m
0xFF
[0;32mI (29763) UART READ: [LEN: ]: 1[0m
[0;32mI (29783) UART_READ: : ERROR[0m
0xFF
[0;32mI (29783) UART READ: [LEN: ]: 1[0m
[0;32mI (29803) UART_READ: : ERROR[0m
0xFF
[0;32mI (29803) UART READ: [LEN: ]: 1[0m
[0;32mI (29823) UART_READ: : ERROR[0m
0xFF
[0;32mI (29823) UART READ: [LEN: ]: 1[0m
[0;32mI (29863) UART_READ: : ERROR[0m
0xFF
[0;32mI (29863) UART READ: [LEN: ]: 1[0m
[0;32mI (29903) UART_READ: : ERROR[0m
0xFF
[0;32mI (29903) UART READ: [LEN: ]: 1[0m
[0;32mI (29923) UART_READ: : ERROR[0m
0xFF
[0;32mI (29923) UART READ: [LEN: ]: 1[0m
[0;32mI (29983) UART_READ: : ERROR[0m
0xFF
[0;32mI (29983) UART READ: [LEN: ]: 1[0m
[0;32mI (30023) UART_READ: : ERROR[0m
0xFF
[0;32mI (30023) UART READ: [LEN: ]: 1[0m
[0;32mI (30043) UART_READ: : ERROR[0m
0xFF
[0;32mI (30043) UART READ: [LEN: ]: 1[0m
[0;32mI (30063) UART_READ: : ERROR[0m
0xFF
[0;32mI (30063) UART READ: [LEN: ]: 1[0m
[0;32mI (30143) UART_READ: : ERROR[0m
0xFF
[0;32mI (30143) UART READ: [LEN: ]: 1[0m
[0;32mI (30183) UART_READ: : ERROR[0m
0xFF
[0;32mI (30183) UART READ: [LEN: ]: 1[0m
[0;32mI (30203) UART_READ: : ERROR[0m
0xFF
[0;32mI (30203) UART READ: [LEN: ]: 1[0m
[0;32mI (30263) UART_READ: : ERROR[0m
0xFF
[0;32mI (30263) UART READ: [LEN: ]: 1[0m
[0;32mI (30283) UART_READ: : ERROR[0m
0xFE
[0;32mI (30283) UART READ: [LEN: ]: 1[0m
[0;32mI (30303) UART_READ: : ERROR[0m
0xFF
[0;32mI (30303) UART READ: [LEN: ]: 1[0m
[0;32mI (30343) UART_READ: : ERROR[0m
0xFF
[0;32mI (30343) UART READ: [LEN: ]: 1[0m
[0;32mI (30383) UART_READ: : ERROR[0m
0xFF
[0;32mI (30383) UART READ: [LEN: ]: 1[0m
[0;32mI (30403) UART_READ: : ERROR[0m
0xFF
[0;32mI (30403) UART READ: [LEN: ]: 1[0m
[0;32mI (30423) UART_READ: : ERROR[0m
0xFF
[0;32mI (30423) UART READ: [LEN: ]: 1[0m
[0;32mI (30443) UART_READ: : ERROR[0m
0xFF
[0;32mI (30443) UART READ: [LEN: ]: 1[0m
[0;32mI (30463) UART_READ: : ERROR[0m
0xFE
[0;32mI (30463) UART READ: [LEN: ]: 1[0m
[0;32mI (30503) UART_READ: : ERROR[0m
0xFF
[0;32mI (30503) UART READ: [LEN: ]: 1[0m
[0;32mI (30523) UART_READ: : ERROR[0m
0xFF
[0;32mI (30523) UART READ: [LEN: ]: 1[0m
[0;32mI (30543) UART_READ: : ERROR[0m
0xFF
[0;32mI (30543) UART READ: [LEN: ]: 1[0m
[0;32mI (30563) UART_READ: : ERROR[0m
0xFF
[0;32mI (30563) UART READ: [LEN: ]: 1[0m
[0;32mI (30583) UART_READ: : ERROR[0m
0xFF
[0;32mI (30583) UART READ: [LEN: ]: 1[0m
[0;32mI (30623) UART_READ: : ERROR[0m
0xFF
[0;32mI (30623) UART READ: [LEN: ]: 1[0m
[0;32mI (30643) UART_READ: : ERROR[0m
0xFF
[0;32mI (30643) UART READ: [LEN: ]: 1[0m
[0;32mI (30663) UART_READ: : ERROR[0m
0xFF
[0;32mI (30663) UART READ: [LEN: ]: 1[0m
[0;32mI (30743) UART_READ: : ERROR[0m
0xFF
[0;32mI (30743) UART READ: [LEN: ]: 1[0m
[0;32mI (30763) UART_READ: : ERROR[0m
0xFF
[0;32mI (30763) UART READ: [LEN: ]: 1[0m
[0;32mI (30783) UART_READ: : ERROR[0m
0xFF
[0;32mI (30783) UART READ: [LEN: ]: 1[0m
[0;32mI (30803) UART_READ: : ERROR[0m
0xFF
[0;32mI (30803) UART READ: [LEN: ]: 1[0m
[0;32mI (30823) UART_READ: : ERROR[0m
0xFF
[0;32mI (30823) UART READ: [LEN: ]: 1[0m
[0;32mI (30843) UART_READ: : ERROR[0m
0xFF
[0;32mI (30843) UART READ: [LEN: ]: 1[0m
[0;32mI (30863) UART_READ: : ERROR[0m
0xFF
[0;32mI (30863) UART READ: [LEN: ]: 1[0m
[0;32mI (30883) UART_READ: : ERROR[0m
0xFF
[0;32mI (30883) UART READ: [LEN: ]: 1[0m
[0;32mI (30903) UART_READ: : ERROR[0m
0xFF
[0;32mI (30903) UART READ: [LEN: ]: 1[0m
[0;32mI (30963) UART_READ: : ERROR[0m
0xFF
[0;32mI (30963) UART READ: [LEN: ]: 1[0m
[0;32mI (30983) UART_READ: : ERROR[0m
0xFF
[0;32mI (30983) UART READ: [LEN: ]: 1[0m
[0;32mI (31043) UART_READ: : ERROR[0m
0xFF
[0;32mI (31043) UART READ: [LEN: ]: 1[0m
[0;32mI (31083) UART_READ: : ERROR[0m
0xFF
[0;32mI (31083) UART READ: [LEN: ]: 1[0m
[0;32mI (31103) UART_READ: : ERROR[0m
0xFF
[0;32mI (31103) UART READ: [LEN: ]: 1[0m
[0;32mI (31123) UART_READ: : ERROR[0m
0xFF
[0;32mI (31123) UART READ: [LEN: ]: 1[0m
[0;32mI (31143) UART_READ: : ERROR[0m
0xFF
[0;32mI (31143) UART READ: [LEN: ]: 1[0m
[0;32mI (31163) UART_READ: : ERROR[0m
0xFE
[0;32mI (31163) UART READ: [LEN: ]: 1[0m
[0;32mI (31183) UART_READ: : ERROR[0m
0xFD
[0;32mI (31183) UART READ: [LEN: ]: 1[0m
[0;32mI (31203) UART_READ: : ERROR[0m
0xFF
[0;32mI (31203) UART READ: [LEN: ]: 1[0m
[0;32mI (31223) UART_READ: : ERROR[0m
0xFE
[0;32mI (31223) UART READ: [LEN: ]: 1[0m
[0;32mI (31243) UART_READ: : ERROR[0m
0xFF
[0;32mI (31243) UART READ: [LEN: ]: 1[0m
[0;32mI (31263) UART_READ: : ERROR[0m
0xFE
[0;32mI (31263) UART READ: [LEN: ]: 1[0m
[0;32mI (31283) UART_READ: : ERROR[0m
0xFF
[0;32mI (31283) UART READ: [LEN: ]: 1[0m
[0;32mI (31363) UART_READ: : ERROR[0m
0xFF
[0;32mI (31363) UART READ: [LEN: ]: 1[0m
[0;32mI (31383) UART_READ: : ERROR[0m
0xFF
[0;32mI (31383) UART READ: [LEN: ]: 1[0m
[0;32mI (31443) UART_READ: : ERROR[0m
0xFF
[0;32mI (31443) UART READ: [LEN: ]: 1[0m
[0;32mI (31463) UART_READ: : ERROR[0m
0xFF
[0;32mI (31463) UART READ: [LEN: ]: 1[0m
[0;32mI (31483) UART_READ: : ERROR[0m
0xFF
[0;32mI (31483) UART READ: [LEN: ]: 1[0m
[0;32mI (31523) UART_READ: : ERROR[0m
0xFF
[0;32mI (31523) UART READ: [LEN: ]: 1[0m
[0;32mI (31543) UART_READ: : ERROR[0m
0xFF
[0;32mI (31543) UART READ: [LEN: ]: 1[0m
[0;32mI (31563) UART_READ: : ERROR[0m
0xFE
[0;32mI (31563) UART READ: [LEN: ]: 1[0m
[0;32mI (31603) UART_READ: : ERROR[0m
0xFF
[0;32mI (31603) UART READ: [LEN: ]: 1[0m
[0;32mI (31623) UART_READ: : ERROR[0m
0xFF
[0;32mI (31623) UART READ: [LEN: ]: 1[0m
[0;32mI (31683) UART_READ: : ERROR[0m
0xFD
[0;32mI (31683) UART READ: [LEN: ]: 1[0m
[0;32mI (31763) UART_READ: : ERROR[0m
0xFE
[0;32mI (31763) UART READ: [LEN: ]: 1[0m
[0;32mI (31783) UART_READ: : ERROR[0m
0xFF
[0;32mI (31783) UART READ: [LEN: ]: 1[0m
[0;32mI (31803) UART_READ: : ERROR[0m
0xFF
[0;32mI (31803) UART READ: [LEN: ]: 1[0m
[0;32mI (31863) UART_READ: : ERROR[0m
0xFA
[0;32mI (31863) UART READ: [LEN: ]: 1[0m
[0;32mI (31883) UART_READ: : ERROR[0m
0xFF
[0;32mI (31883) UART READ: [LEN: ]: 1[0m
[0;32mI (32043) UART_READ: : ERROR[0m
0xFF
[0;32mI (32043) UART READ: [LEN: ]: 1[0m
[0;32mI (32063) UART_READ: : ERROR[0m
0xFF
[0;32mI (32063) UART READ: [LEN: ]: 1[0m
[0;32mI (32123) UART_READ: : ERROR[0m
0xFF
[0;32mI (32123) UART READ: [LEN: ]: 1[0m
[0;32mI (32163) UART_READ: : ERROR[0m
0xFF
[0;32mI (32163) UART READ: [LEN: ]: 1[0m
[0;32mI (32183) UART_READ: : ERROR[0m
0xFF
[0;32mI (32183) UART READ: [LEN: ]: 1[0m
[0;32mI (32203) UART_READ: : ERROR[0m
0xFF
[0;32mI (32203) UART READ: [LEN: ]: 1[0m
[0;32mI (32383) UART_READ: : ERROR[0m
0xFE
[0;32mI (32383) UART READ: [LEN: ]: 1[0m
[0;32mI (32463) UART_READ: : ERROR[0m
0xFF
[0;32mI (32463) UART READ: [LEN: ]: 1[0m
[0;32mI (32483) UART_READ: : ERROR[0m
0xFF
[0;32mI (32483) UART READ: [LEN: ]: 1[0m
[0;32mI (32503) UART_READ: : ERROR[0m
0xFF
[0;32mI (32503) UART READ: [LEN: ]: 1[0m
[0;32mI (32523) UART_READ: : ERROR[0m
0xFF
[0;32mI (32523) UART READ: [LEN: ]: 1[0m
[0;32mI (32563) UART_READ: : ERROR[0m
0xFF
[0;32mI (32563) UART READ: [LEN: ]: 1[0m
[0;32mI (32723) UART_READ: : ERROR[0m
0xFF
[0;32mI (32723) UART READ: [LEN: ]: 1[0m
[0;32mI (32743) UART_READ: : ERROR[0m
0xFF
[0;32mI (32743) UART READ: [LEN: ]: 1[0m
[0;32mI (32783) UART_READ: : ERROR[0m
0xFF
[0;32mI (32783) UART READ: [LEN: ]: 1[0m
[0;32mI (32803) UART_READ: : ERROR[0m
0xFF
[0;32mI (32803) UART READ: [LEN: ]: 1[0m
[0;32mI (32823) UART_READ: : ERROR[0m
0xFF
[0;32mI (32823) UART READ: [LEN: ]: 1[0m
[0;32mI (32843) UART_READ: : ERROR[0m
0xFF
[0;32mI (32843) UART READ: [LEN: ]: 1[0m
[0;32mI (32903) UART_READ: : ERROR[0m
0xFF
[0;32mI (32903) UART READ: [LEN: ]: 1[0m
[0;32mI (32923) UART_READ: : ERROR[0m
0xFF
[0;32mI (32923) UART READ: [LEN: ]: 1[0m
[0;32mI (32943) UART_READ: : ERROR[0m
0xFF
[0;32mI (32943) UART READ: [LEN: ]: 1[0m
[0;32mI (32963) UART_READ: : ERROR[0m
0xFF
[0;32mI (32963) UART READ: [LEN: ]: 1[0m
[0;32mI (33023) UART_READ: : ERROR[0m
0xFF
[0;32mI (33023) UART READ: [LEN: ]: 1[0m
[0;32mI (33063) UART_READ: : ERROR[0m
0xFD
[0;32mI (33063) UART READ: [LEN: ]: 1[0m
[0;32mI (33163) UART_READ: : ERROR[0m
0xFE
[0;32mI (33163) UART READ: [LEN: ]: 1[0m
[0;32mI (33243) UART_READ: : ERROR[0m
0xFF
[0;32mI (33243) UART READ: [LEN: ]: 1[0m
[0;32mI (33263) UART_READ: : ERROR[0m
0xFF
[0;32mI (33263) UART READ: [LEN: ]: 1[0m
[0;32mI (33283) UART_READ: : ERROR[0m
0xFF
[0;32mI (33283) UART READ: [LEN: ]: 1[0m
[0;32mI (33323) UART_READ: : ERROR[0m
0xFF
[0;32mI (33323) UART READ: [LEN: ]: 1[0m
[0;32mI (33343) UART_READ: : ERROR[0m
0xFF
[0;32mI (33343) UART READ: [LEN: ]: 1[0m
[0;32mI (33363) UART_READ: : ERROR[0m
0xFE
[0;32mI (33363) UART READ: [LEN: ]: 1[0m
[0;32mI (33383) UART_READ: : ERROR[0m
0xFB
[0;32mI (33383) UART READ: [LEN: ]: 1[0m
[0;32mI (33403) UART_READ: : ERROR[0m
0xFD
[0;32mI (33403) UART READ: [LEN: ]: 1[0m
[0;32mI (33443) UART_READ: : ERROR[0m
0xFD
[0;32mI (33443) UART READ: [LEN: ]: 1[0m
[0;32mI (33483) UART_READ: : ERROR[0m
0xFE
[0;32mI (33483) UART READ: [LEN: ]: 1[0m
[0;32mI (33563) UART_READ: : ERROR[0m
0xFF
[0;32mI (33563) UART READ: [LEN: ]: 1[0m
[0;32mI (33583) UART_READ: : ERROR[0m
0xFE
[0;32mI (33583) UART READ: [LEN: ]: 1[0m
[0;32mI (33603) UART_READ: : ERROR[0m
0xFE
[0;32mI (33603) UART READ: [LEN: ]: 1[0m
[0;32mI (33623) UART_READ: : ERROR[0m
0xFF
[0;32mI (33623) UART READ: [LEN: ]: 1[0m
[0;32mI (33643) UART_READ: : ERROR[0m
0xFF
[0;32mI (33643) UART READ: [LEN: ]: 1[0m
[0;32mI (33683) UART_READ: : ERROR[0m
0xFF
[0;32mI (33683) UART READ: [LEN: ]: 1[0m
[0;32mI (33703) UART_READ: : ERROR[0m
0xFD
[0;32mI (33703) UART READ: [LEN: ]: 1[0m
[0;32mI (33723) UART_READ: : ERROR[0m
0xFE
[0;32mI (33723) UART READ: [LEN: ]: 1[0m
[0;32mI (33743) UART_READ: : ERROR[0m
0xFF
[0;32mI (33743) UART READ: [LEN: ]: 1[0m
[0;32mI (33843) UART_READ: : ERROR[0m
0xFF
[0;32mI (33843) UART READ: [LEN: ]: 1[0m
[0;32mI (33863) UART_READ: : ERROR[0m
0xFE
[0;32mI (33863) UART READ: [LEN: ]: 1[0m
[0;32mI (33923) UART_READ: : ERROR[0m
0xFE
[0;32mI (33923) UART READ: [LEN: ]: 1[0m
[0;32mI (33943) UART_READ: : ERROR[0m
0xFD
[0;32mI (33943) UART READ: [LEN: ]: 1[0m
[0;32mI (34003) UART_READ: : ERROR[0m
0xFF
[0;32mI (34003) UART READ: [LEN: ]: 1[0m
[0;32mI (34083) UART_READ: : ERROR[0m
0xFF
[0;32mI (34083) UART READ: [LEN: ]: 1[0m
[0;32mI (34103) UART_READ: : ERROR[0m
0xFD
[0;32mI (34103) UART READ: [LEN: ]: 1[0m
[0;32mI (34143) UART_READ: : ERROR[0m
0xFF
[0;32mI (34143) UART READ: [LEN: ]: 1[0m
[0;32mI (34183) UART_READ: : ERROR[0m
0xFB
[0;32mI (34183) UART READ: [LEN: ]: 1[0m
[0;32mI (34203) UART_READ: : ERROR[0m
0xFF
[0;32mI (34203) UART READ: [LEN: ]: 1[0m
[0;32mI (34223) UART_READ: : ERROR[0m
0xFF
[0;32mI (34223) UART READ: [LEN: ]: 1[0m
[0;32mI (34243) UART_READ: : ERROR[0m
0xFB
[0;32mI (34243) UART READ: [LEN: ]: 1[0m
[0;32mI (34403) UART_READ: : ERROR[0m
0xFF
[0;32mI (34403) UART READ: [LEN: ]: 1[0m
[0;32mI (34483) UART_READ: : ERROR[0m
0xFF
[0;32mI (34483) UART READ: [LEN: ]: 1[0m
[0;32mI (34503) UART_READ: : ERROR[0m
0xFF
[0;32mI (34503) UART READ: [LEN: ]: 1[0m
[0;32mI (34523) UART_READ: : ERROR[0m
0xFF
[0;32mI (34523) UART READ: [LEN: ]: 1[0m
[0;32mI (34563) UART_READ: : ERROR[0m
0xFE
[0;32mI (34563) UART READ: [LEN: ]: 1[0m
[0;32mI (34583) UART_READ: : ERROR[0m
0xFF
[0;32mI (34583) UART READ: [LEN: ]: 1[0m
[0;32mI (34603) UART_READ: : ERROR[0m
0xFF
[0;32mI (34603) UART READ: [LEN: ]: 1[0m
[0;32mI (34623) UART_READ: : ERROR[0m
0xFE
[0;32mI (34623) UART READ: [LEN: ]: 1[0m
[0;32mI (34683) UART_READ: : ERROR[0m
0xFF
[0;32mI (34683) UART READ: [LEN: ]: 1[0m
[0;32mI (34703) UART_READ: : ERROR[0m
0xFD
[0;32mI (34703) UART READ: [LEN: ]: 1[0m
[0;32mI (34723) UART_READ: : ERROR[0m
0xFF
[0;32mI (34723) UART READ: [LEN: ]: 1[0m
[0;32mI (34783) UART_READ: : ERROR[0m
0xFF
[0;32mI (34783) UART READ: [LEN: ]: 1[0m
[0;32mI (34843) UART_READ: : ERROR[0m
0xFF
[0;32mI (34843) UART READ: [LEN: ]: 1[0m
[0;32mI (34863) UART_READ: : ERROR[0m
0xFF
[0;32mI (34863) UART READ: [LEN: ]: 1[0m
[0;32mI (34883) UART_READ: : ERROR[0m
0xFF
[0;32mI (34883) UART READ: [LEN: ]: 1[0m
[0;32mI (34923) UART_READ: : ERROR[0m
0xFD
[0;32mI (34923) UART READ: [LEN: ]: 1[0m
[0;32mI (34963) UART_READ: : ERROR[0m
0xFF
[0;32mI (34963) UART READ: [LEN: ]: 1[0m
[0;32mI (34983) UART_READ: : ERROR[0m
0xFE
[0;32mI (34983) UART READ: [LEN: ]: 1[0m
[0;32mI (35063) UART_READ: : ERROR[0m
0xFF
[0;32mI (35063) UART READ: [LEN: ]: 1[0m
[0;32mI (35183) UART_READ: : ERROR[0m
0xFF
[0;32mI (35183) UART READ: [LEN: ]: 1[0m
[0;32mI (35223) UART_READ: : ERROR[0m
0xFF
[0;32mI (35223) UART READ: [LEN: ]: 1[0m
[0;32mI (35243) UART_READ: : ERROR[0m
0xFF
[0;32mI (35243) UART READ: [LEN: ]: 1[0m
[0;32mI (35263) UART_READ: : ERROR[0m
0xFF
[0;32mI (35263) UART READ: [LEN: ]: 1[0m
[0;32mI (35283) UART_READ: : ERROR[0m
0xFF
[0;32mI (35283) UART READ: [LEN: ]: 1[0m
[0;32mI (35303) UART_READ: : ERROR[0m
0xFE
[0;32mI (35303) UART READ: [LEN: ]: 1[0m
[0;32mI (35323) UART_READ: : ERROR[0m
0xFF
[0;32mI (35323) UART READ: [LEN: ]: 1[0m
[0;32mI (35383) UART_READ: : ERROR[0m
0xFE
[0;32mI (35383) UART READ: [LEN: ]: 1[0m
[0;32mI (35443) UART_READ: : ERROR[0m
0xFF
[0;32mI (35443) UART READ: [LEN: ]: 1[0m
[0;32mI (35463) UART_READ: : ERROR[0m
0xFE
[0;32mI (35463) UART READ: [LEN: ]: 1[0m
[0;32mI (35523) UART_READ: : ERROR[0m
0xFE
[0;32mI (35523) UART READ: [LEN: ]: 1[0m
[0;32mI (35603) UART_READ: : ERROR[0m
0xFF
[0;32mI (35603) UART READ: [LEN: ]: 1[0m
[0;32mI (35643) UART_READ: : ERROR[0m
0xFF
[0;32mI (35643) UART READ: [LEN: ]: 1[0m
[0;32mI (35683) UART_READ: : ERROR[0m
0xFF
[0;32mI (35683) UART READ: [LEN: ]: 1[0m
[0;32mI (35743) UART_READ: : ERROR[0m
0xFF
[0;32mI (35743) UART READ: [LEN: ]: 1[0m
[0;32mI (35783) UART_READ: : ERROR[0m
0xFE
[0;32mI (35783) UART READ: [LEN: ]: 1[0m
[0;32mI (35823) UART_READ: : ERROR[0m
0xFE
[0;32mI (35823) UART READ: [LEN: ]: 1[0m
[0;32mI (35843) UART_READ: : ERROR[0m
0xFF
[0;32mI (35843) UART READ: [LEN: ]: 1[0m
[0;32mI (35983) UART_READ: : ERROR[0m
0xFF
[0;32mI (35983) UART READ: [LEN: ]: 1[0m
[0;32mI (36043) UART_READ: : ERROR[0m
0xFF
[0;32mI (36043) UART READ: [LEN: ]: 1[0m
[0;32mI (36063) UART_READ: : ERROR[0m
0xFF
[0;32mI (36063) UART READ: [LEN: ]: 1[0m
[0;32mI (36123) UART_READ: : ERROR[0m
0xFF
[0;32mI (36123) UART READ: [LEN: ]: 1[0m
[0;32mI (36143) UART_READ: : ERROR[0m
0xFF
[0;32mI (36143) UART READ: [LEN: ]: 1[0m
[0;32mI (36183) UART_READ: : ERROR[0m
0xFE
[0;32mI (36183) UART READ: [LEN: ]: 1[0m
[0;32mI (36303) UART_READ: : ERROR[0m
0xFF
[0;32mI (36303) UART READ: [LEN: ]: 1[0m
[0;32mI (36363) UART_READ: : ERROR[0m
0xFF
[0;32mI (36363) UART READ: [LEN: ]: 1[0m
[0;32mI (36443) UART_READ: : ERROR[0m
0xFF
[0;32mI (36443) UART READ: [LEN: ]: 1[0m
[0;32mI (36463) UART_READ: : ERROR[0m
0xFF
[0;32mI (36463) UART READ: [LEN: ]: 1[0m
[0;32mI (36483) UART_READ: : ERROR[0m
0xFE
[0;32mI (36483) UART READ: [LEN: ]: 1[0m
[0;32mI (36563) UART_READ: : ERROR[0m
0xFF
[0;32mI (36563) UART READ: [LEN: ]: 1[0m
[0;32mI (36583) UART_READ: : ERROR[0m
0xFB
[0;32mI (36583) UART READ: [LEN: ]: 1[0m
[0;32mI (36603) UART_READ: : ERROR[0m
0xFF
[0;32mI (36603) UART READ: [LEN: ]: 1[0m
[0;32mI (36663) UART_READ: : ERROR[0m
0xFF
[0;32mI (36663) UART READ: [LEN: ]: 1[0m
[0;32mI (36683) UART_READ: : ERROR[0m
0xFB
[0;32mI (36683) UART READ: [LEN: ]: 1[0m
[0;32mI (36703) UART_READ: : ERROR[0m
0xFE
[0;32mI (36703) UART READ: [LEN: ]: 1[0m
[0;32mI (36723) UART_READ: : ERROR[0m
0xFC
[0;32mI (36723) UART READ: [LEN: ]: 1[0m
[0;32mI (36743) UART_READ: : ERROR[0m
0xFC
[0;32mI (36743) UART READ: [LEN: ]: 1[0m
[0;32mI (36783) UART_READ: : ERROR[0m
0xFE
[0;32mI (36783) UART READ: [LEN: ]: 1[0m
[0;32mI (36843) UART_READ: : ERROR[0m
0xFF
[0;32mI (36843) UART READ: [LEN: ]: 1[0m
[0;32mI (36923) UART_READ: : ERROR[0m
0xFF
[0;32mI (36923) UART READ: [LEN: ]: 1[0m
[0;32mI (36943) UART_READ: : ERROR[0m
0xFF
[0;32mI (36943) UART READ: [LEN: ]: 1[0m
[0;32mI (36983) UART_READ: : ERROR[0m
0xFE
[0;32mI (36983) UART READ: [LEN: ]: 1[0m
[0;32mI (37003) UART_READ: : ERROR[0m
0xFE
[0;32mI (37003) UART READ: [LEN: ]: 1[0m
[0;32mI (37043) UART_READ: : ERROR[0m
0xFF
[0;32mI (37043) UART READ: [LEN: ]: 1[0m
[0;32mI (37083) UART_READ: : ERROR[0m
0xFF
[0;32mI (37083) UART READ: [LEN: ]: 1[0m
[0;32mI (37103) UART_READ: : ERROR[0m
0xF8
[0;32mI (37103) UART READ: [LEN: ]: 1[0m
[0;32mI (37123) UART_READ: : ERROR[0m
0xFD
[0;32mI (37123) UART READ: [LEN: ]: 1[0m
[0;32mI (37143) UART_READ: : ERROR[0m
0xFF
[0;32mI (37143) UART READ: [LEN: ]: 1[0m
[0;32mI (37243) UART_READ: : ERROR[0m
0xFD
[0;32mI (37243) UART READ: [LEN: ]: 1[0m
[0;32mI (37403) UART_READ: : ERROR[0m
0xFE
[0;32mI (37403) UART READ: [LEN: ]: 1[0m
[0;32mI (37483) UART_READ: : ERROR[0m
0xFF
[0;32mI (37483) UART READ: [LEN: ]: 1[0m
[0;32mI (37503) UART_READ: : ERROR[0m
0xFA
[0;32mI (37503) UART READ: [LEN: ]: 1[0m
[0;32mI (37623) UART_READ: : ERROR[0m
0xFE
[0;32mI (37623) UART READ: [LEN: ]: 1[0m
[0;32mI (37663) UART_READ: : ERROR[0m
0xFF
[0;32mI (37663) UART READ: [LEN: ]: 1[0m
[0;32mI (37683) UART_READ: : ERROR[0m
0xFF
[0;32mI (37683) UART READ: [LEN: ]: 1[0m
[0;32mI (37703) UART_READ: : ERROR[0m
0xFE
[0;32mI (37703) UART READ: [LEN: ]: 1[0m
[0;32mI (37723) UART_READ: : ERROR[0m
0xFB
[0;32mI (37723) UART READ: [LEN: ]: 1[0m
[0;32mI (37763) UART_READ: : ERROR[0m
0xFE
[0;32mI (37763) UART READ: [LEN: ]: 1[0m
[0;32mI (37883) UART_READ: : ERROR[0m
0xFA
[0;32mI (37883) UART READ: [LEN: ]: 1[0m
[0;32mI (37963) UART_READ: : ERROR[0m
0xF9
[0;32mI (37963) UART READ: [LEN: ]: 1[0m
[0;32mI (37983) UART_READ: : ERROR[0m
0xFC
[0;32mI (37983) UART READ: [LEN: ]: 1[0m
[0;32mI (38043) UART_READ: : ERROR[0m
0xFC
[0;32mI (38043) UART READ: [LEN: ]: 1[0m
[0;32mI (38083) UART_READ: : ERROR[0m
0xFF
[0;32mI (38083) UART READ: [LEN: ]: 1[0m
[0;32mI (38103) UART_READ: : ERROR[0m
0xFE
[0;32mI (38103) UART READ: [LEN: ]: 1[0m
[0;32mI (38223) UART_READ: : ERROR[0m
0xF8
[0;32mI (38223) UART READ: [LEN: ]: 1[0m
[0;32mI (38263) UART_READ: : ERROR[0m
0xFC
[0;32mI (38263) UART READ: [LEN: ]: 1[0m
[0;32mI (38323) UART_READ: : ERROR[0m
0xFB
[0;32mI (38323) UART READ: [LEN: ]: 1[0m
[0;32mI (38423) UART_READ: : ERROR[0m
0xFF
[0;32mI (38423) UART READ: [LEN: ]: 1[0m
[0;32mI (38443) UART_READ: : ERROR[0m
0xFF
[0;32mI (38443) UART READ: [LEN: ]: 1[0m
[0;32mI (38583) UART_READ: : ERROR[0m
0xFF
[0;32mI (38583) UART READ: [LEN: ]: 1[0m
[0;32mI (38623) UART_READ: : ERROR[0m
0xFC
[0;32mI (38623) UART READ: [LEN: ]: 1[0m
[0;32mI (38683) UART_READ: : ERROR[0m
0xFE
[0;32mI (38683) UART READ: [LEN: ]: 1[0m
[0;32mI (38763) UART_READ: : ERROR[0m
0xFC
[0;32mI (38763) UART READ: [LEN: ]: 1[0m
[0;32mI (38783) UART_READ: : ERROR[0m
0xFE
[0;32mI (38783) UART READ: [LEN: ]: 1[0m
[0;32mI (38923) UART_READ: : ERROR[0m
0xFE
[0;32mI (38923) UART READ: [LEN: ]: 1[0m
[0;32mI (38963) UART_READ: : ERROR[0m
0xFF
[0;32mI (38963) UART READ: [LEN: ]: 1[0m
[0;32mI (39023) UART_READ: : ERROR[0m
0xFF
[0;32mI (39023) UART READ: [LEN: ]: 1[0m
[0;32mI (39183) UART_READ: : ERROR[0m
0xFE
[0;32mI (39183) UART READ: [LEN: ]: 1[0m
[0;32mI (39223) UART_READ: : ERROR[0m
0xFA
[0;32mI (39223) UART READ: [LEN: ]: 1[0m
[0;32mI (39243) UART_READ: : ERROR[0m
0xFF
[0;32mI (39243) UART READ: [LEN: ]: 1[0m
[0;32mI (39303) UART_READ: : ERROR[0m
0xFC
[0;32mI (39303) UART READ: [LEN: ]: 1[0m
[0;32mI (39423) UART_READ: : ERROR[0m
0xF9
[0;32mI (39423) UART READ: [LEN: ]: 1[0m
[0;32mI (39443) UART_READ: : ERROR[0m
0xFE
[0;32mI (39443) UART READ: [LEN: ]: 1[0m
[0;32mI (39483) UART_READ: : ERROR[0m
0xFE
[0;32mI (39483) UART READ: [LEN: ]: 1[0m
[0;32mI (39603) UART_READ: : ERROR[0m
0xFF
[0;32mI (39603) UART READ: [LEN: ]: 1[0m
[0;32mI (39663) UART_READ: : ERROR[0m
0xFF
[0;32mI (39663) UART READ: [LEN: ]: 1[0m
[0;32mI (39703) UART_READ: : ERROR[0m
0xFC
[0;32mI (39703) UART READ: [LEN: ]: 1[0m
[0;32mI (39723) UART_READ: : ERROR[0m
0xFE
[0;32mI (39723) UART READ: [LEN: ]: 1[0m
[0;32mI (39743) UART_READ: : ERROR[0m
0xF8
[0;32mI (39743) UART READ: [LEN: ]: 1[0m
[0;32mI (39763) UART_READ: : ERROR[0m
0xFF
[0;32mI (39763) UART READ: [LEN: ]: 1[0m
[0;32mI (39803) UART_READ: : ERROR[0m
0xFE
[0;32mI (39803) UART READ: [LEN: ]: 1[0m
[0;32mI (39883) UART_READ: : ERROR[0m
0xFE
[0;32mI (39883) UART READ: [LEN: ]: 1[0m
[0;32mI (39903) UART_READ: : ERROR[0m
0xFE
[0;32mI (39903) UART READ: [LEN: ]: 1[0m
[0;32mI (39923) UART_READ: : ERROR[0m
0xFE
[0;32mI (39923) UART READ: [LEN: ]: 1[0m
[0;32mI (39943) UART_READ: : ERROR[0m
0xF9
[0;32mI (39943) UART READ: [LEN: ]: 1[0m
[0;32mI (39963) UART_READ: : ERROR[0m
0xFE
[0;32mI (39963) UART READ: [LEN: ]: 1[0m
[0;32mI (39983) UART_READ: : ERROR[0m
0xFC
[0;32mI (39983) UART READ: [LEN: ]: 1[0m
[0;32mI (40003) UART_READ: : ERROR[0m
0xFB
[0;32mI (40003) UART READ: [LEN: ]: 1[0m
[0;32mI (40063) UART_READ: : ERROR[0m
0xF8
[0;32mI (40063) UART READ: [LEN: ]: 1[0m
[0;32mI (40083) UART_READ: : ERROR[0m
0xF9
[0;32mI (40083) UART READ: [LEN: ]: 1[0m
[0;32mI (40143) UART_READ: : ERROR[0m
0xF8
[0;32mI (40143) UART READ: [LEN: ]: 1[0m
[0;32mI (40183) UART_READ: : ERROR[0m
0xF8
[0;32mI (40183) UART READ: [LEN: ]: 1[0m
[0;32mI (40203) UART_READ: : ERROR[0m
0xFE
[0;32mI (40203) UART READ: [LEN: ]: 1[0m
[0;32mI (40223) UART_READ: : ERROR[0m
0xFF
[0;32mI (40223) UART READ: [LEN: ]: 1[0m
[0;32mI (40303) UART_READ: : ERROR[0m
0xFF
[0;32mI (40303) UART READ: [LEN: ]: 1[0m
[0;32mI (40323) UART_READ: : ERROR[0m
0xFE
[0;32mI (40323) UART READ: [LEN: ]: 1[0m
[0;32mI (40383) UART_READ: : ERROR[0m
0xFE
[0;32mI (40383) UART READ: [LEN: ]: 1[0m
[0;32mI (40483) UART_READ: : ERROR[0m
0xFF
[0;32mI (40483) UART READ: [LEN: ]: 1[0m
[0;32mI (40523) UART_READ: : ERROR[0m
0xF8
[0;32mI (40523) UART READ: [LEN: ]: 1[0m
[0;32mI (40543) UART_READ: : ERROR[0m
0xFE
[0;32mI (40543) UART READ: [LEN: ]: 1[0m
[0;32mI (40563) UART_READ: : ERROR[0m
0xFE
[0;32mI (40563) UART READ: [LEN: ]: 1[0m
[0;32mI (40763) UART_READ: : ERROR[0m
0xFF
[0;32mI (40763) UART READ: [LEN: ]: 1[0m
[0;32mI (40843) UART_READ: : ERROR[0m
0xF8
[0;32mI (40843) UART READ: [LEN: ]: 1[0m
[0;32mI (40883) UART_READ: : ERROR[0m
0xFF
[0;32mI (40883) UART READ: [LEN: ]: 1[0m
[0;32mI (40903) UART_READ: : ERROR[0m
0xFF
[0;32mI (40903) UART READ: [LEN: ]: 1[0m
[0;32mI (40963) UART_READ: : ERROR[0m
0xFE
[0;32mI (40963) UART READ: [LEN: ]: 1[0m
[0;32mI (40983) UART_READ: : ERROR[0m
0xF8
[0;32mI (40983) UART READ: [LEN: ]: 1[0m
[0;32mI (41023) UART_READ: : ERROR[0m
0xFE
[0;32mI (41023) UART READ: [LEN: ]: 1[0m
[0;32mI (41043) UART_READ: : ERROR[0m
0xFE
[0;32mI (41043) UART READ: [LEN: ]: 1[0m
[0;32mI (41063) UART_READ: : ERROR[0m
0xFE
[0;32mI (41063) UART READ: [LEN: ]: 1[0m
[0;32mI (41123) UART_READ: : ERROR[0m
0xFC
[0;32mI (41123) UART READ: [LEN: ]: 1[0m
[0;32mI (41143) UART_READ: : ERROR[0m
0xFE
[0;32mI (41143) UART READ: [LEN: ]: 1[0m
[0;32mI (41223) UART_READ: : ERROR[0m
0xFE
[0;32mI (41223) UART READ: [LEN: ]: 1[0m
[0;32mI (41283) UART_READ: : ERROR[0m
0xF9
[0;32mI (41283) UART READ: [LEN: ]: 1[0m
[0;32mI (41403) UART_READ: : ERROR[0m
0xFE
[0;32mI (41403) UART READ: [LEN: ]: 1[0m
[0;32mI (41503) UART_READ: : ERROR[0m
0xFD
[0;32mI (41503) UART READ: [LEN: ]: 1[0m
[0;32mI (41523) UART_READ: : ERROR[0m
0xFE
[0;32mI (41523) UART READ: [LEN: ]: 1[0m
[0;32mI (41563) UART_READ: : ERROR[0m
0xFE
[0;32mI (41563) UART READ: [LEN: ]: 1[0m
[0;32mI (41603) UART_READ: : ERROR[0m
0xFE
[0;32mI (41603) UART READ: [LEN: ]: 1[0m
[0;32mI (41683) UART_READ: : ERROR[0m
0xF8
[0;32mI (41683) UART READ: [LEN: ]: 1[0m
[0;32mI (41703) UART_READ: : ERROR[0m
0xFE
[0;32mI (41703) UART READ: [LEN: ]: 1[0m
[0;32mI (41783) UART_READ: : ERROR[0m
0xFC
[0;32mI (41783) UART READ: [LEN: ]: 1[0m
[0;32mI (41803) UART_READ: : ERROR[0m
0xFF
[0;32mI (41803) UART READ: [LEN: ]: 1[0m
[0;32mI (41823) UART_READ: : ERROR[0m
0xFF
[0;32mI (41823) UART READ: [LEN: ]: 1[0m
[0;32mI (41843) UART_READ: : ERROR[0m
0xFF
[0;32mI (41843) UART READ: [LEN: ]: 1[0m
[0;32mI (41983) UART_READ: : ERROR[0m
0xFE
[0;32mI (41983) UART READ: [LEN: ]: 1[0m
[0;32mI (42103) UART_READ: : ERROR[0m
0xFF
[0;32mI (42103) UART READ: [LEN: ]: 1[0m
[0;32mI (42163) UART_READ: : ERROR[0m
0xF9
[0;32mI (42163) UART READ: [LEN: ]: 1[0m
[0;32mI (42183) UART_READ: : ERROR[0m
0xFE
[0;32mI (42183) UART READ: [LEN: ]: 1[0m
[0;32mI (42263) UART_READ: : ERROR[0m
0xFE
[0;32mI (42263) UART READ: [LEN: ]: 1[0m
[0;32mI (42283) UART_READ: : ERROR[0m
0xFF
[0;32mI (42283) UART READ: [LEN: ]: 1[0m
[0;32mI (42403) UART_READ: : ERROR[0m
0xFC
[0;32mI (42403) UART READ: [LEN: ]: 1[0m
[0;32mI (42463) UART_READ: : ERROR[0m
0xFF
[0;32mI (42463) UART READ: [LEN: ]: 1[0m
[0;32mI (42523) UART_READ: : ERROR[0m
0xFC
[0;32mI (42523) UART READ: [LEN: ]: 1[0m
[0;32mI (42603) UART_READ: : ERROR[0m
0xFC
[0;32mI (42603) UART READ: [LEN: ]: 1[0m
[0;32mI (42623) UART_READ: : ERROR[0m
0xF8
[0;32mI (42623) UART READ: [LEN: ]: 1[0m
[0;32mI (42663) UART_READ: : ERROR[0m
0xFC
[0;32mI (42663) UART READ: [LEN: ]: 1[0m
[0;32mI (42723) UART_READ: : ERROR[0m
0xFF
[0;32mI (42723) UART READ: [LEN: ]: 1[0m
[0;32mI (42803) UART_READ: : ERROR[0m
0xFC
[0;32mI (42803) UART READ: [LEN: ]: 1[0m
[0;32mI (42823) UART_READ: : ERROR[0m
0xFF
[0;32mI (42823) UART READ: [LEN: ]: 1[0m
[0;32mI (42963) UART_READ: : ERROR[0m
0xFF
[0;32mI (42963) UART READ: [LEN: ]: 1[0m
[0;32mI (42983) UART_READ: : ERROR[0m
0xFE
[0;32mI (42983) UART READ: [LEN: ]: 1[0m
[0;32mI (43003) UART_READ: : ERROR[0m
0xFC
[0;32mI (43003) UART READ: [LEN: ]: 1[0m
[0;32mI (43023) UART_READ: : ERROR[0m
0xFE
[0;32mI (43023) UART READ: [LEN: ]: 1[0m
[0;32mI (43063) UART_READ: : ERROR[0m
0xFF
[0;32mI (43063) UART READ: [LEN: ]: 1[0m
[0;32mI (43103) UART_READ: : ERROR[0m
0xFC
[0;32mI (43103) UART READ: [LEN: ]: 1[0m
[0;32mI (43123) UART_READ: : ERROR[0m
0xF9
[0;32mI (43123) UART READ: [LEN: ]: 1[0m
[0;32mI (43143) UART_READ: : ERROR[0m
0xFE
[0;32mI (43143) UART READ: [LEN: ]: 1[0m
[0;32mI (43163) UART_READ: : ERROR[0m
0xFF
[0;32mI (43163) UART READ: [LEN: ]: 1[0m
[0;32mI (43233) UART_READ: : ERROR[0m
0xFE
[0;32mI (43243) UART READ: [LEN: ]: 1[0m
[0;32mI (43263) UART_READ: : ERROR[0m
0xFE
[0;32mI (43263) UART READ: [LEN: ]: 1[0m
[0;32mI (43273) UART_READ: : ERROR[0m
0xFF
[0;32mI (43283) UART READ: [LEN: ]: 1[0m
[0;32mI (43373) UART_READ: : ERROR[0m
0xFF
[0;32mI (43383) UART READ: [LEN: ]: 1[0m
[0;32mI (43453) UART_READ: : ERROR[0m
0xFC
[0;32mI (43463) UART READ: [LEN: ]: 1[0m
[0;32mI (43473) UART_READ: : ERROR[0m
0xFC
[0;32mI (43483) UART READ: [LEN: ]: 1[0m
[0;32mI (43493) UART_READ: : ERROR[0m
0xFE
[0;32mI (43503) UART READ: [LEN: ]: 1[0m
[0;32mI (43513) UART_READ: : ERROR[0m
0xFF
[0;32mI (43523) UART READ: [LEN: ]: 1[0m
[0;32mI (43613) UART_READ: : ERROR[0m
0xFC
[0;32mI (43623) UART READ: [LEN: ]: 1[0m
[0;32mI (43813) UART_READ: : ERROR[0m
0xFE
[0;32mI (43823) UART READ: [LEN: ]: 1[0m
[0;32mI (43893) UART_READ: : ERROR[0m
0xFE
[0;32mI (43893) UART READ: [LEN: ]: 1[0m
[0;32mI (43993) UART_READ: : ERROR[0m
0xFF
[0;32mI (43993) UART READ: [LEN: ]: 1[0m
[0;32mI (44033) UART_READ: : ERROR[0m
0xFF
[0;32mI (44033) UART READ: [LEN: ]: 1[0m
[0;32mI (44053) UART_READ: : ERROR[0m
0xFE
[0;32mI (44053) UART READ: [LEN: ]: 1[0m
[0;32mI (44073) UART_READ: : ERROR[0m
0xFE
[0;32mI (44073) UART READ: [LEN: ]: 1[0m
[0;32mI (44173) UART_READ: : ERROR[0m
0xFF
[0;32mI (44173) UART READ: [LEN: ]: 1[0m
[0;32mI (44213) UART_READ: : ERROR[0m
0xFE
[0;32mI (44213) UART READ: [LEN: ]: 1[0m
[0;32mI (44293) UART_READ: : ERROR[0m
0xFF
[0;32mI (44293) UART READ: [LEN: ]: 1[0m
[0;32mI (44313) UART_READ: : ERROR[0m
0xFF
[0;32mI (44313) UART READ: [LEN: ]: 1[0m
[0;32mI (44373) UART_READ: : ERROR[0m
0xFE
[0;32mI (44373) UART READ: [LEN: ]: 1[0m
[0;32mI (44593) UART_READ: : ERROR[0m
0xFE
[0;32mI (44593) UART READ: [LEN: ]: 1[0m
[0;32mI (44673) UART_READ: : ERROR[0m
0xFC
[0;32mI (44673) UART READ: [LEN: ]: 1[0m
[0;32mI (44693) UART_READ: : ERROR[0m
0xFF
[0;32mI (44693) UART READ: [LEN: ]: 1[0m
[0;32mI (44773) UART_READ: : ERROR[0m
0xFF
[0;32mI (44773) UART READ: [LEN: ]: 1[0m
[0;32mI (44853) UART_READ: : ERROR[0m
0xFF
[0;32mI (44853) UART READ: [LEN: ]: 1[0m
[0;32mI (45013) UART_READ: : ERROR[0m
0xFF
[0;32mI (45013) UART READ: [LEN: ]: 1[0m
[0;32mI (45073) UART_READ: : ERROR[0m
0xFF
[0;32mI (45073) UART READ: [LEN: ]: 1[0m
[0;32mI (45133) UART_READ: : ERROR[0m
0xFD
[0;32mI (45133) UART READ: [LEN: ]: 1[0m
[0;32mI (45153) UART_READ: : ERROR[0m
0xFF
[0;32mI (45153) UART READ: [LEN: ]: 1[0m
[0;32mI (45173) UART_READ: : ERROR[0m
0xFF
[0;32mI (45173) UART READ: [LEN: ]: 1[0m
[0;32mI (45253) UART_READ: : ERROR[0m
0xFF
[0;32mI (45253) UART READ: [LEN: ]: 1[0m
[0;32mI (45293) UART_READ: : ERROR[0m
0xFF
[0;32mI (45293) UART READ: [LEN: ]: 1[0m
[0;32mI (45313) UART_READ: : ERROR[0m
0xFD
[0;32mI (45313) UART READ: [LEN: ]: 1[0m
[0;32mI (45333) UART_READ: : ERROR[0m
0xFE
[0;32mI (45333) UART READ: [LEN: ]: 1[0m
[0;32mI (45353) UART_READ: : ERROR[0m
0xFF
[0;32mI (45353) UART READ: [LEN: ]: 1[0m
[0;32mI (45433) UART_READ: : ERROR[0m
0xFF
[0;32mI (45433) UART READ: [LEN: ]: 1[0m
[0;32mI (45453) UART_READ: : ERROR[0m
0xFD
[0;32mI (45453) UART READ: [LEN: ]: 1[0m
[0;32mI (45473) UART_READ: : ERROR[0m
0xFD
[0;32mI (45473) UART READ: [LEN: ]: 1[0m
[0;32mI (45493) UART_READ: : ERROR[0m
0xFE
[0;32mI (45493) UART READ: [LEN: ]: 1[0m
[0;32mI (45513) UART_READ: : ERROR[0m
0xFE
[0;32mI (45513) UART READ: [LEN: ]: 1[0m
[0;32mI (45553) UART_READ: : ERROR[0m
0xFF
[0;32mI (45553) UART READ: [LEN: ]: 1[0m
[0;32mI (45573) UART_READ: : ERROR[0m
0xFE
[0;32mI (45573) UART READ: [LEN: ]: 1[0m
[0;32mI (45593) UART_READ: : ERROR[0m
0xFF
[0;32mI (45593) UART READ: [LEN: ]: 1[0m
[0;32mI (45613) UART_READ: : ERROR[0m
0xFF
[0;32mI (45613) UART READ: [LEN: ]: 1[0m
[0;32mI (45653) UART_READ: : ERROR[0m
0xFF
[0;32mI (45653) UART READ: [LEN: ]: 1[0m
[0;32mI (45673) UART_READ: : ERROR[0m
0xFF
[0;32mI (45673) UART READ: [LEN: ]: 1[0m
[0;32mI (45693) UART_READ: : ERROR[0m
0xFF
[0;32mI (45693) UART READ: [LEN: ]: 1[0m
[0;32mI (45753) UART_READ: : ERROR[0m
0xFF
[0;32mI (45753) UART READ: [LEN: ]: 1[0m
[0;32mI (45793) UART_READ: : ERROR[0m
0xFD
[0;32mI (45793) UART READ: [LEN: ]: 1[0m
[0;32mI (45833) UART_READ: : ERROR[0m
0xFF
[0;32mI (45833) UART READ: [LEN: ]: 1[0m
[0;32mI (45853) UART_READ: : ERROR[0m
0xFF
[0;32mI (45853) UART READ: [LEN: ]: 1[0m
[0;32mI (45873) UART_READ: : ERROR[0m
0xFF
[0;32mI (45873) UART READ: [LEN: ]: 1[0m
[0;32mI (45893) UART_READ: : ERROR[0m
0xFF
[0;32mI (45893) UART READ: [LEN: ]: 1[0m
[0;32mI (45933) UART_READ: : ERROR[0m
0xFF
[0;32mI (45933) UART READ: [LEN: ]: 1[0m
[0;32mI (45973) UART_READ: : ERROR[0m
0xFE
[0;32mI (45973) UART READ: [LEN: ]: 1[0m
[0;32mI (46013) UART_READ: : ERROR[0m
0xFF
[0;32mI (46013) UART READ: [LEN: ]: 1[0m
[0;32mI (46113) UART_READ: : ERROR[0m
0xFF
[0;32mI (46113) UART READ: [LEN: ]: 1[0m
[0;32mI (46133) UART_READ: : ERROR[0m
0xFE
[0;32mI (46133) UART READ: [LEN: ]: 1[0m
[0;32mI (46153) UART_READ: : ERROR[0m
0xFF
[0;32mI (46153) UART READ: [LEN: ]: 1[0m
[0;32mI (46173) UART_READ: : ERROR[0m
0xFF
[0;32mI (46173) UART READ: [LEN: ]: 1[0m
[0;32mI (46193) UART_READ: : ERROR[0m
0xFF
[0;32mI (46193) UART READ: [LEN: ]: 1[0m
[0;32mI (46253) UART_READ: : ERROR[0m
0xFF
[0;32mI (46253) UART READ: [LEN: ]: 1[0m
[0;32mI (46273) UART_READ: : ERROR[0m
0xFF
[0;32mI (46273) UART READ: [LEN: ]: 1[0m
[0;32mI (46293) UART_READ: : ERROR[0m
0xFF
[0;32mI (46293) UART READ: [LEN: ]: 1[0m
[0;32mI (46393) UART_READ: : ERROR[0m
0xFE
[0;32mI (46393) UART READ: [LEN: ]: 1[0m
[0;32mI (46413) UART_READ: : ERROR[0m
0xFF
[0;32mI (46413) UART READ: [LEN: ]: 1[0m
[0;32mI (46533) UART_READ: : ERROR[0m
0xFF
[0;32mI (46533) UART READ: [LEN: ]: 1[0m
[0;32mI (46553) UART_READ: : ERROR[0m
0xFF
[0;32mI (46553) UART READ: [LEN: ]: 1[0m
[0;32mI (46593) UART_READ: : ERROR[0m
0xFF
[0;32mI (46593) UART READ: [LEN: ]: 1[0m
[0;32mI (46653) UART_READ: : ERROR[0m
0xFF
[0;32mI (46653) UART READ: [LEN: ]: 1[0m
[0;32mI (46773) UART_READ: : ERROR[0m
0xFF
[0;32mI (46773) UART READ: [LEN: ]: 1[0m
[0;32mI (46813) UART_READ: : ERROR[0m
0xFF
[0;32mI (46813) UART READ: [LEN: ]: 1[0m
[0;32mI (46833) UART_READ: : ERROR[0m
0xFF
[0;32mI (46833) UART READ: [LEN: ]: 1[0m
[0;32mI (46993) UART_READ: : ERROR[0m
0xFF
[0;32mI (46993) UART READ: [LEN: ]: 1[0m
[0;32mI (47013) UART_READ: : ERROR[0m
0xFF
[0;32mI (47013) UART READ: [LEN: ]: 1[0m
[0;32mI (47053) UART_READ: : ERROR[0m
0xFD
[0;32mI (47053) UART READ: [LEN: ]: 1[0m
[0;32mI (47073) UART_READ: : ERROR[0m
0xFF
[0;32mI (47073) UART READ: [LEN: ]: 1[0m
[0;32mI (47133) UART_READ: : ERROR[0m
0xFF
[0;32mI (47133) UART READ: [LEN: ]: 1[0m
[0;32mI (47353) UART_READ: : ERROR[0m
0xFF
[0;32mI (47353) UART READ: [LEN: ]: 1[0m
[0;32mI (47373) UART_READ: : ERROR[0m
0xFF
[0;32mI (47373) UART READ: [LEN: ]: 1[0m
[0;32mI (47413) UART_READ: : ERROR[0m
0xFF
[0;32mI (47413) UART READ: [LEN: ]: 1[0m
[0;32mI (47433) UART_READ: : ERROR[0m
0xFF
[0;32mI (47433) UART READ: [LEN: ]: 1[0m
[0;32mI (47453) UART_READ: : ERROR[0m
0xFF
[0;32mI (47453) UART READ: [LEN: ]: 1[0m
[0;32mI (47493) UART_READ: : ERROR[0m
0xFF
[0;32mI (47493) UART READ: [LEN: ]: 1[0m
[0;32mI (47533) UART_READ: : ERROR[0m
0xFF
[0;32mI (47533) UART READ: [LEN: ]: 1[0m
[0;32mI (47573) UART_READ: : ERROR[0m
0xFF
[0;32mI (47573) UART READ: [LEN: ]: 1[0m
[0;32mI (47613) UART_READ: : ERROR[0m
0xFF
[0;32mI (47613) UART READ: [LEN: ]: 1[0m
[0;32mI (47633) UART_READ: : ERROR[0m
0xFF
[0;32mI (47633) UART READ: [LEN: ]: 1[0m
[0;32mI (47713) UART_READ: : ERROR[0m
0xFF
[0;32mI (47713) UART READ: [LEN: ]: 1[0m
[0;32mI (47773) UART_READ: : ERROR[0m
0xFF
[0;32mI (47773) UART READ: [LEN: ]: 1[0m
[0;32mI (47833) UART_READ: : ERROR[0m
0xFF
[0;32mI (47833) UART READ: [LEN: ]: 1[0m
[0;32mI (47853) UART_READ: : ERROR[0m
0xFF
[0;32mI (47853) UART READ: [LEN: ]: 1[0m
[0;32mI (47873) UART_READ: : ERROR[0m
0xFF
[0;32mI (47873) UART READ: [LEN: ]: 1[0m
[0;32mI (47913) UART_READ: : ERROR[0m
0xFF
[0;32mI (47913) UART READ: [LEN: ]: 1[0m
[0;32mI (47933) UART_READ: : ERROR[0m
0xFF
[0;32mI (47933) UART READ: [LEN: ]: 1[0m
[0;32mI (48033) UART_READ: : ERROR[0m
0xFF
[0;32mI (48033) UART READ: [LEN: ]: 1[0m
[0;32mI (48173) UART_READ: : ERROR[0m
0xFF
[0;32mI (48173) UART READ: [LEN: ]: 1[0m
[0;32mI (48193) UART_READ: : ERROR[0m
0xFF
[0;32mI (48193) UART READ: [LEN: ]: 1[0m
[0;32mI (48213) UART_READ: : ERROR[0m
0xFF
[0;32mI (48213) UART READ: [LEN: ]: 1[0m
[0;32mI (48233) UART_READ: : ERROR[0m
0xFF
[0;32mI (48233) UART READ: [LEN: ]: 1[0m
[0;32mI (48273) UART_READ: : ERROR[0m
0xFF
[0;32mI (48273) UART READ: [LEN: ]: 1[0m
[0;32mI (48293) UART_READ: : ERROR[0m
0xFF
[0;32mI (48293) UART READ: [LEN: ]: 1[0m
[0;32mI (48313) UART_READ: : ERROR[0m
0xFE
[0;32mI (48313) UART READ: [LEN: ]: 1[0m
[0;32mI (48333) UART_READ: : ERROR[0m
0xFF
[0;32mI (48333) UART READ: [LEN: ]: 1[0m
[0;32mI (48513) UART_READ: : ERROR[0m
0xFF
[0;32mI (48513) UART READ: [LEN: ]: 1[0m
[0;32mI (48533) UART_READ: : ERROR[0m
0xFF
[0;32mI (48533) UART READ: [LEN: ]: 1[0m
[0;32mI (48613) UART_READ: : ERROR[0m
0xFF
[0;32mI (48613) UART READ: [LEN: ]: 1[0m
[0;32mI (48633) UART_READ: : ERROR[0m
0xFF
[0;32mI (48633) UART READ: [LEN: ]: 1[0m
[0;32mI (48713) UART_READ: : ERROR[0m
0xFF
[0;32mI (48713) UART READ: [LEN: ]: 1[0m
[0;32mI (48793) UART_READ: : ERROR[0m
0xFF
[0;32mI (48793) UART READ: [LEN: ]: 1[0m
[0;32mI (48813) UART_READ: : ERROR[0m
0xFF
[0;32mI (48813) UART READ: [LEN: ]: 1[0m
[0;32mI (48833) UART_READ: : ERROR[0m
0xFF
[0;32mI (48833) UART READ: [LEN: ]: 1[0m
[0;32mI (48853) UART_READ: : ERROR[0m
0xFF
[0;32mI (48853) UART READ: [LEN: ]: 1[0m
[0;32mI (48873) UART_READ: : ERROR[0m
0xFF
[0;32mI (48873) UART READ: [LEN: ]: 1[0m
[0;32mI (48933) UART_READ: : ERROR[0m
0xFF
[0;32mI (48933) UART READ: [LEN: ]: 1[0m
[0;32mI (48973) UART_READ: : ERROR[0m
0xFF
[0;32mI (48973) UART READ: [LEN: ]: 1[0m
[0;32mI (48993) UART_READ: : ERROR[0m
0xFF
[0;32mI (48993) UART READ: [LEN: ]: 1[0m
[0;32mI (49013) UART_READ: : ERROR[0m
0xFF
[0;32mI (49013) UART READ: [LEN: ]: 1[0m
[0;32mI (49033) UART_READ: : ERROR[0m
0xFF
[0;32mI (49033) UART READ: [LEN: ]: 1[0m
[0;32mI (49053) UART_READ: : ERROR[0m
0xFF
[0;32mI (49053) UART READ: [LEN: ]: 1[0m
[0;32mI (49093) UART_READ: : ERROR[0m
0xFF
[0;32mI (49093) UART READ: [LEN: ]: 1[0m
[0;32mI (49153) UART_READ: : ERROR[0m
0xFF
[0;32mI (49153) UART READ: [LEN: ]: 1[0m
[0;32mI (49173) UART_READ: : ERROR[0m
0xFF
[0;32mI (49173) UART READ: [LEN: ]: 1[0m
[0;32mI (49213) UART_READ: : ERROR[0m
0xFE
[0;32mI (49213) UART READ: [LEN: ]: 1[0m
[0;32mI (49253) UART_READ: : ERROR[0m
0xFF
[0;32mI (49253) UART READ: [LEN: ]: 1[0m
[0;32mI (49273) UART_READ: : ERROR[0m
0xFF
[0;32mI (49273) UART READ: [LEN: ]: 1[0m
[0;32mI (49473) UART_READ: : ERROR[0m
0xFF
[0;32mI (49473) UART READ: [LEN: ]: 1[0m
[0;32mI (49493) UART_READ: : ERROR[0m
0xFF
[0;32mI (49493) UART READ: [LEN: ]: 1[0m
[0;32mI (49533) UART_READ: : ERROR[0m
0xFF
[0;32mI (49533) UART READ: [LEN: ]: 1[0m
[0;32mI (49553) UART_READ: : ERROR[0m
0xFF
[0;32mI (49553) UART READ: [LEN: ]: 1[0m
[0;32mI (49633) UART_READ: : ERROR[0m
0xFF
[0;32mI (49633) UART READ: [LEN: ]: 1[0m
[0;32mI (49713) UART_READ: : ERROR[0m
0xFF
[0;32mI (49713) UART READ: [LEN: ]: 1[0m
[0;32mI (49733) UART_READ: : ERROR[0m
0xFF
[0;32mI (49733) UART READ: [LEN: ]: 1[0m
[0;32mI (49773) UART_READ: : ERROR[0m
0xFF
[0;32mI (49773) UART READ: [LEN: ]: 1[0m
[0;32mI (49793) UART_READ: : ERROR[0m
0xFE
[0;32mI (49793) UART READ: [LEN: ]: 1[0m
[0;32mI (49813) UART_READ: : ERROR[0m
0xFF
[0;32mI (49813) UART READ: [LEN: ]: 1[0m
[0;32mI (49833) UART_READ: : ERROR[0m
0xFF
[0;32mI (49833) UART READ: [LEN: ]: 1[0m
[0;32mI (49873) UART_READ: : ERROR[0m
0xFF
[0;32mI (49873) UART READ: [LEN: ]: 1[0m
[0;32mI (49893) UART_READ: : ERROR[0m
0xFF
[0;32mI (49893) UART READ: [LEN: ]: 1[0m
[0;32mI (49913) UART_READ: : ERROR[0m
0xFF
[0;32mI (49913) UART READ: [LEN: ]: 1[0m
[0;32mI (49933) UART_READ: : ERROR[0m
0xFF
[0;32mI (49933) UART READ: [LEN: ]: 1[0m
[0;32mI (50013) UART_READ: : ERROR[0m
0xFF
[0;32mI (50013) UART READ: [LEN: ]: 1[0m
[0;32mI (50033) UART_READ: : ERROR[0m
0xFF
[0;32mI (50033) UART READ: [LEN: ]: 1[0m
[0;32mI (50073) UART_READ: : ERROR[0m
0xFF
[0;32mI (50073) UART READ: [LEN: ]: 1[0m
[0;32mI (50093) UART_READ: : ERROR[0m
0xFF
[0;32mI (50093) UART READ: [LEN: ]: 1[0m
[0;32mI (50153) UART_READ: : ERROR[0m
0xFF
[0;32mI (50153) UART READ: [LEN: ]: 1[0m
[0;32mI (50213) UART_READ: : ERROR[0m
0xFF
[0;32mI (50213) UART READ: [LEN: ]: 1[0m
[0;32mI (50233) UART_READ: : ERROR[0m
0xFF
[0;32mI (50233) UART READ: [LEN: ]: 1[0m
[0;32mI (50253) UART_READ: : ERROR[0m
0xFF
[0;32mI (50253) UART READ: [LEN: ]: 1[0m
[0;32mI (50273) UART_READ: : ERROR[0m
0xFE
[0;32mI (50273) UART READ: [LEN: ]: 1[0m
[0;32mI (50313) UART_READ: : ERROR[0m
0xFF
[0;32mI (50313) UART READ: [LEN: ]: 1[0m
[0;32mI (50333) UART_READ: : ERROR[0m
0xFF
[0;32mI (50333) UART READ: [LEN: ]: 1[0m
[0;32mI (50353) UART_READ: : ERROR[0m
0xFF
[0;32mI (50353) UART READ: [LEN: ]: 1[0m
[0;32mI (50373) UART_READ: : ERROR[0m
0xFF
[0;32mI (50373) UART READ: [LEN: ]: 1[0m
[0;32mI (50413) UART_READ: : ERROR[0m
0xFF
[0;32mI (50413) UART READ: [LEN: ]: 1[0m
[0;32mI (50433) UART_READ: : ERROR[0m
0xFF
[0;32mI (50433) UART READ: [LEN: ]: 1[0m
[0;32mI (50453) UART_READ: : ERROR[0m
0xFF
[0;32mI (50453) UART READ: [LEN: ]: 1[0m
[0;32mI (50513) UART_READ: : ERROR[0m
0xFF
[0;32mI (50513) UART READ: [LEN: ]: 1[0m
[0;32mI (50573) UART_READ: : ERROR[0m
0xFF
[0;32mI (50573) UART READ: [LEN: ]: 1[0m
[0;32mI (50653) UART_READ: : ERROR[0m
0xFF
[0;32mI (50653) UART READ: [LEN: ]: 1[0m
[0;32mI (50673) UART_READ: : ERROR[0m
0xFF
[0;32mI (50673) UART READ: [LEN: ]: 1[0m
[0;32mI (50693) UART_READ: : ERROR[0m
0xFF
[0;32mI (50693) UART READ: [LEN: ]: 1[0m
[0;32mI (50713) UART_READ: : ERROR[0m
0xFF
[0;32mI (50713) UART READ: [LEN: ]: 1[0m
[0;32mI (50733) UART_READ: : ERROR[0m
0xFF
[0;32mI (50733) UART READ: [LEN: ]: 1[0m
[0;32mI (50793) UART_READ: : ERROR[0m
0xFF
[0;32mI (50793) UART READ: [LEN: ]: 1[0m
[0;32mI (50813) UART_READ: : ERROR[0m
0xFF
[0;32mI (50813) UART READ: [LEN: ]: 1[0m
[0;32mI (50853) UART_READ: : ERROR[0m
0xFF
[0;32mI (50853) UART READ: [LEN: ]: 1[0m
[0;32mI (50913) UART_READ: : ERROR[0m
0xFF
[0;32mI (50913) UART READ: [LEN: ]: 1[0m
[0;32mI (50953) UART_READ: : ERROR[0m
0xFF
[0;32mI (50953) UART READ: [LEN: ]: 1[0m
[0;32mI (51013) UART_READ: : ERROR[0m
0xFF
[0;32mI (51013) UART READ: [LEN: ]: 1[0m
[0;32mI (51033) UART_READ: : ERROR[0m
0xFF
[0;32mI (51033) UART READ: [LEN: ]: 1[0m
[0;32mI (51053) UART_READ: : ERROR[0m
0xFF
[0;32mI (51053) UART READ: [LEN: ]: 1[0m
[0;32mI (51073) UART_READ: : ERROR[0m
0xFF
[0;32mI (51073) UART READ: [LEN: ]: 1[0m
[0;32mI (51113) UART_READ: : ERROR[0m
0xFF
[0;32mI (51113) UART READ: [LEN: ]: 1[0m
[0;32mI (51133) UART_READ: : ERROR[0m
0xFF
[0;32mI (51133) UART READ: [LEN: ]: 1