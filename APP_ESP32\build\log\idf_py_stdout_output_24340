[1/5] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/partition_table/partition-table.bin D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_1_0.bin"
Vantage_nxESP32_Int_Rel_1_2_1_0.bin binary size 0x97120 bytes. Smallest app partition is 0x100000 bytes. 0x68ee0 bytes (41%) free.

[2/5] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x51e0 bytes. 0x2e20 bytes (36%) free.


[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.4.1\esp-idf\components\esptool_py && C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.4.1/esp-idf -D SERIAL_TOOL=C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build -P C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32s3 -p COM26 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size detect 0x0 bootloader/bootloader.bin 0x10000 Vantage_nxESP32_Int_Rel_1_2_1_0.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port COM26
Connecting....
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)
Crystal is 40MHz
MAC: a0:85:e3:f1:8c:c4
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Auto-detected Flash size: 16MB
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x000a7fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 20960 bytes to 13374...
Writing at 0x00000000... (100 %)
Wrote 20960 bytes (13374 compressed) at 0x00000000 in 0.8 seconds (effective 207.1 kbit/s)...
Hash of data verified.
Compressed 618784 bytes to 363593...
Writing at 0x00010000... (4 %)
Writing at 0x0001ca0a... (8 %)
Writing at 0x00026bd7... (13 %)
Writing at 0x0002fbed... (17 %)
Writing at 0x0003599f... (21 %)
Writing at 0x0003c1f6... (26 %)
Writing at 0x00042507... (30 %)
Writing at 0x00047e7a... (34 %)
Writing at 0x0004e3bb... (39 %)
Writing at 0x00054564... (43 %)
Writing at 0x0005a0c6... (47 %)
Writing at 0x0005f9fc... (52 %)
Writing at 0x00065405... (56 %)
Writing at 0x0006b703... (60 %)
Writing at 0x000714a4... (65 %)
Writing at 0x00077768... (69 %)
Writing at 0x0007d1d9... (73 %)
Writing at 0x00083731... (78 %)
Writing at 0x0008d97c... (82 %)
Writing at 0x00093d23... (86 %)
Writing at 0x00099239... (91 %)
Writing at 0x0009f2d0... (95 %)
Writing at 0x000a59c8... (100 %)
Wrote 618784 bytes (363593 compressed) at 0x00010000 in 9.0 seconds (effective 548.1 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 118...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (118 compressed) at 0x00008000 in 0.1 seconds (effective 297.7 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
