/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_app.c
* @version        : 1.0.7
* @brief          : BLE handler file
* @details        : BLE handler file
********************************************************************************
* @version 1.0.0                                         				Date : 09/07/2025
* [*] Initial version with BLE GAP sync and advertising start/stop
********************************************************************************
* @version 1.0.1                                         				Date : 21/07/2025
* [+] Added ble_advertisement_start(), ble_advertisement_stop(), is_ble_synced()
* [+] Added NVS state management for BLE advertising persistence
* [~] Changed clear_state_from_nvs() from static to public
* [+] Added send_ble_status_to_nexus for BLE_CONNECTED and BLE_DISCONNECTED
********************************************************************************
* @version 1.0.2                                         				Date : 23/07/2025
* [+] Integrated LED control for BLE status (connected, advertising, TX, error)
********************************************************************************
* @version 1.0.3                                         				Date : 24/07/2025
* [+] Added BLE bonding state management functions for NVS persistence
* [+] Added set_bonding_state_to_nvs(), clear_bonding_state_from_nvs(), load_bonding_state_from_nvs()
* [-] Removed BLE advertising state NVS functions (set_state_to_nvs, clear_state_from_nvs)
********************************************************************************
* @version 1.0.4                                         				Date : 25/07/2025
* [+] Enhanced GAP event handling for better bonding support
* [+] Added BLE_GAP_EVENT_IDENTITY_RESOLVED handling
* [+] Improved BLE_GAP_EVENT_PASSKEY_ACTION with auto-accept for NoInputNoOutput
********************************************************************************
* @version 1.0.5                                         				Date : 26/07/2025
* [+] Complete BLE bonding implementation with dynamic security configuration
* [+] Added enable_ble_bonding(), disable_ble_bonding(), clear_all_bonding_info()
* [+] Added get_bonding_status() with bonded device counting
* [+] Enhanced security_initialization() to load bonding state from NVS on startup
* [+] Dynamic security configuration based on NVS bonding state
* [+] Secure Connections enabled when bonding is active
********************************************************************************
* @version 1.0.6                                         				Date : 26/07/2025
* [~] Simplified BLE bonding implementation - always enabled for mobile devices
* [-] Removed dynamic bonding control functions and UART commands
* [~] Modified security_initialization() to permanently enable BLE bonding
* [+] BLE bonding and Secure Connections always enabled for ESP32 ↔ Mobile connections
* [+] Simplified architecture: no external control needed, bonding always available
********************************************************************************
* @version 1.0.7                                         				Date : 26/07/2025
* [-] Completely removed BLE bonding logic - replaced with device address verification
* [+] Implemented device address verification system using NVS storage
* [+] Added store_device_address_to_nvs() - stores connected device's BLE address
* [+] Added load_device_address_from_nvs() - loads stored device address for verification
* [+] Added clear_device_address_from_nvs() - clears stored device address
* [+] Added compare_ble_addresses() - compares two BLE addresses for equality
* [~] Modified BLE_GAP_EVENT_LINK_ESTAB to verify device identity on each connection
* [~] Disabled BLE bonding completely (sm_bonding = 0, sm_sc = 0)
* [+] First connection stores device address, subsequent connections verify against stored address
********************************************************************************/

#include <stdbool.h>
#include "ble_app.h"
#include "esp_err.h"
#include "gatt_svr.h"
#include "esp_mac.h"
#include "console/console.h"
#include "driver/gpio.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "nvs.h" // Include NVS header
#include <inttypes.h> // For PRId32 format specifier
#include "services/gap/ble_svc_gap.h"
#include "ble_response_handler.h"
#include <utility.h>
#include "led_control.h"

/* DEFINES */

/* VARIABLES */
static bool ble_is_synced = false;
static bool stop_adv_on_disconnect_flag = false; // Flag to stop advertising on disconnect

/* FUNCTION PROTOTYPE */
static const char *tag = "NimBLE_BLE_PRPH";
static int ble_gap_event(struct ble_gap_event *event, void *arg);
static void security_initialization(void);
static uint8_t own_addr_type;
void ble_store_config_init(void);
void ble_advertisement_start(void);
void ble_advertisement_stop(void);
bool is_ble_synced(void);

/**
 * @brief Starts BLE advertising.
 *
 * This function configures and starts BLE advertising. It sets up the 
 * advertisement data (device name, services, etc.) and makes the device 
 * discoverable and connectable to BLE clients.
 *
 * @note This should be called after the BLE stack is synchronized and 
 * the GATT server has been initialized.
 *
 * @return None
 */
static void ble_advertisement(void) {
	struct ble_gap_adv_params adv_params;
	struct ble_hs_adv_fields fields;
	const char *name;
	uint8_t baseMac[6];
	int32_t rc;

	memset(&fields, 0, sizeof fields);

	/* Advertise two flags:
	 *     o Discoverability in forthcoming advertisement (general)
	 *     o BLE-only (BR/EDR unsupported).
	 */
	fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;

	/* Indicate that the TX power level field should be included; have the
	 * stack fill this value automatically.  This is done by assigning the
	 * special value BLE_HS_ADV_TX_PWR_LVL_AUTO.
	 */
	name = "CT CAR 2";
	// name = ble_svc_gap_device_name();
	fields.name = (uint8_t *)name;
	fields.name_len = strlen(name);
	fields.name_is_complete = 1;

	esp_read_mac(baseMac, ESP_MAC_BT);
	const char * mfg_data = (const char *)baseMac;
	fields.mfg_data = (uint8_t *)mfg_data;
	fields.mfg_data_len = strlen(mfg_data);

	rc = ble_gap_adv_set_fields(&fields);
	if (rc != 0)
	{
		MODLOG_DFLT(ERROR, "error setting advertisement data; rc=%ld\n", rc);
		return;
	}

	/* Begin advertising. */
	memset(&adv_params, 0, sizeof adv_params);
	adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;
	adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;
	rc = ble_gap_adv_start(own_addr_type, NULL, BLE_HS_FOREVER, &adv_params,
						   ble_gap_event, NULL);
	if (rc != 0)
	{
		MODLOG_DFLT(ERROR, "error enabling advertisement; rc=%ld\n", rc);
		return;
	}
}

/**
 * @brief The nimble host executes this callback when a GAP event occurs.  The
 * application associates a GAP event callback with each connection that forms.
 * bleprph uses the same callback for all connections.
 *
 * @param event                 The type of event being signalled.
 * @param ctxt                  Various information pertaining to the event.
 * @param arg                   Application-specified argument; unused by
 *                                  bleprph.
 *
 * @return                      0 if the application successfully handled the
 *                                  event; nonzero on failure.  The semantics
 *                                  of the return code is specific to the
 *                                  particular GAP event being signalled.
 */
static int ble_gap_event(struct ble_gap_event *event, void *arg) {
	struct ble_gap_conn_desc desc;
	int32_t rc;

	switch (event->type)
	{
	case BLE_GAP_EVENT_LINK_ESTAB:
		printf("BLE CONNECTION ESTABLISHED\n");
		/* A new connection was established or a connection attempt failed. */
		MODLOG_DFLT(INFO, "connection %s; status=%d ",
					event->link_estab.status == 0 ? "established" : "failed",
					event->link_estab.status);
		if (event->link_estab.status == 0)
		{
			led_set_ble_status(LED_STATE_ON);  // Blue LED: Solid when connected

			rc = ble_gap_conn_find(event->link_estab.conn_handle, &desc);
			assert(rc == 0);

			// Device address verification
			ble_addr_t stored_addr;
			esp_err_t nvs_err = load_device_address_from_nvs(&stored_addr);

			if (nvs_err == ESP_ERR_NVS_NOT_FOUND) {
				// First time connection - store this device's address
				ESP_LOGI(tag, "First time connection - storing device address");
				store_device_address_to_nvs(&desc.peer_id_addr);
			} else if (nvs_err == ESP_OK) {
				// Check if this is the same device
				if (compare_ble_addresses(&stored_addr, &desc.peer_id_addr)) {
					ESP_LOGI(tag, "Verified device - same as previously connected");
				} else {
					ESP_LOGW(tag, "Different device detected!");
					ESP_LOGW(tag, "Stored: %02x:%02x:%02x:%02x:%02x:%02x (type: %d)",
							 stored_addr.val[5], stored_addr.val[4], stored_addr.val[3],
							 stored_addr.val[2], stored_addr.val[1], stored_addr.val[0], stored_addr.type);
					ESP_LOGW(tag, "Current: %02x:%02x:%02x:%02x:%02x:%02x (type: %d)",
							 desc.peer_id_addr.val[5], desc.peer_id_addr.val[4], desc.peer_id_addr.val[3],
							 desc.peer_id_addr.val[2], desc.peer_id_addr.val[1], desc.peer_id_addr.val[0], desc.peer_id_addr.type);
				}
			} else {
				ESP_LOGE(tag, "Error loading stored device address: %s", esp_err_to_name(nvs_err));
			}

			send_ble_status_to_nexus(BLE_CONNECTED, NULL, 0);
		}
		else
		{
			led_set_ble_status(LED_STATE_OFF);  // BLUE LED: OFF when Failed to connect

			/* Connection failed; resume advertising. */
            ble_advertisement_start();
		}
		MODLOG_DFLT(INFO, "\n");
		set_connection_handle(event->link_estab.conn_handle);

		// ble_gap_set_prefered_le_phy(event->link_estab.conn_handle, BLE_HCI_LE_PHY_2M_PREF_MASK, BLE_HCI_LE_PHY_2M_PREF_MASK, 0);
		/* Try to update connection parameters */
		struct ble_gap_upd_params params = {.itvl_min = 6,
											.itvl_max = 8,
											.latency = 0,
											.supervision_timeout = 400 };
		rc = ble_gap_update_params(event->connect.conn_handle, &params);

		return 0;

	case BLE_GAP_EVENT_DISCONNECT:
		MODLOG_DFLT(INFO, "disconnect; reason=%d ", event->disconnect.reason);
		MODLOG_DFLT(INFO, "\n");
		set_connection_handle(0);

		led_set_ble_status(LED_STATE_OFF);  // BLUE LED: OFF for No connection

		// send disconnect response
		send_ble_status_to_nexus(BLE_DISCONNECTED, NULL, 0);
		/* Connection terminated; resume advertising. */
		if (get_stop_adv_on_disconnect_flag())
		{
			ble_advertisement_stop();
			set_stop_adv_on_disconnect_flag(false);
		}
		else
		{
			ble_advertisement_start();
		}
		return 0;

	case BLE_GAP_EVENT_CONN_UPDATE:
		/* The central has updated the connection parameters. */
		MODLOG_DFLT(INFO, "connection updated; status=%d ",
					event->conn_update.status);
		rc = ble_gap_conn_find(event->conn_update.conn_handle, &desc);
		assert(rc == 0);
		MODLOG_DFLT(INFO, "\n");
		return 0;

	case BLE_GAP_EVENT_ADV_COMPLETE:
		MODLOG_DFLT(INFO, "advertise complete; reason=%d",
					event->adv_complete.reason);
		ble_advertisement_start();
		return 0;

	case BLE_GAP_EVENT_ENC_CHANGE:
		/* Encryption has been enabled or disabled for this connection. */
		MODLOG_DFLT(INFO, "encryption change event; status=%d ",
					event->enc_change.status);
		rc = ble_gap_conn_find(event->enc_change.conn_handle, &desc);
		assert(rc == 0);
		MODLOG_DFLT(INFO, "\n");
		return 0;

	case BLE_GAP_EVENT_NOTIFY_TX:
		MODLOG_DFLT(INFO,
					"notify_tx event; conn_handle=%d attr_handle=%d "
					"status=%d is_indication=%d",
					event->notify_tx.conn_handle, event->notify_tx.attr_handle,
					event->notify_tx.status, event->notify_tx.indication);

					led_set_ble_status(LED_STATE_BLINK_SLOW);  // BLUE LED: Slow blink during BLE data TX
		return 0;

	case BLE_GAP_EVENT_SUBSCRIBE:
		MODLOG_DFLT(INFO,
					"subscribe event; conn_handle=%d attr_handle=%d "
					"reason=%d prevn=%d curn=%d previ=%d curi=%d\n",
					event->subscribe.conn_handle, event->subscribe.attr_handle,
					event->subscribe.reason, event->subscribe.prev_notify,
					event->subscribe.cur_notify, event->subscribe.prev_indicate,
					event->subscribe.cur_indicate);
		return 0;

	case BLE_GAP_EVENT_MTU:
		MODLOG_DFLT(INFO, "mtu update event; conn_handle=%d cid=%d mtu=%d\n",
					event->mtu.conn_handle, event->mtu.channel_id,
					event->mtu.value);
		return 0;

	case BLE_GAP_EVENT_REPEAT_PAIRING:
		/* We already have a bond with the peer, but it is attempting to
		 * establish a new secure link.  This app sacrifices security for
		 * convenience: just throw away the old bond and accept the new link.
		 */

		/* Delete the old bond. */
		rc = ble_gap_conn_find(event->repeat_pairing.conn_handle, &desc);
		assert(rc == 0);
		ble_store_util_delete_peer(&desc.peer_id_addr);

		/* Return BLE_GAP_REPEAT_PAIRING_RETRY to indicate that the host should
		 * continue with the pairing operation.
		 */
		return BLE_GAP_REPEAT_PAIRING_RETRY;

	case BLE_GAP_EVENT_PASSKEY_ACTION:
		ESP_LOGI(tag, "PASSKEY_ACTION_EVENT started - action=%d",
				 event->passkey.params.action);

		/* For NoInputNoOutput, we typically don't need to handle passkey */
		if (event->passkey.params.action == BLE_SM_IOACT_NUMCMP) {
			/* Numeric comparison - auto-accept for NoInputNoOutput */
			struct ble_sm_io pkey = {0};
			pkey.action = event->passkey.params.action;
			pkey.numcmp_accept = 1;
			rc = ble_sm_inject_io(event->passkey.conn_handle, &pkey);
			ESP_LOGI(tag, "Auto-accepted numeric comparison, rc=%ld", rc);
		}
		return 0;

	case BLE_GAP_EVENT_IDENTITY_RESOLVED:
		ESP_LOGI(tag, "Identity resolved");
		rc = ble_gap_conn_find(event->identity_resolved.conn_handle, &desc);
		assert(rc == 0);
		return 0;

	case BLE_GAP_EVENT_AUTHORIZE:
		MODLOG_DFLT(INFO,
					"authorize event: conn_handle=%d attr_handle=%d is_read=%d",
					event->authorize.conn_handle, event->authorize.attr_handle,
					event->authorize.is_read);

		/* The default behaviour for the event is to reject authorize request */
		event->authorize.out_response = BLE_GAP_AUTHORIZE_REJECT;
		return 0;
	}
	return 0;
}

/**
 * @brief Callback invoked when the BLE host stack resets.
 *
 * This function is called when the BLE host encounters a fatal error 
 * and performs a reset. It is useful for logging the reason and 
 * potentially triggering corrective actions or a full system reset.
 *
 * @param reason   Reset reason code. Matches one of the BLE host error codes.
 *
 * @return         None
 */
static void ble_on_reset(int reason) {
	MODLOG_DFLT(ERROR, "Resetting state; reason=%d\n", reason);
}

/**
 * @brief Callback invoked when the BLE stack is synchronized.
 *
 * This function is called when the NimBLE host and controller are 
 * fully synchronized and the BLE stack is ready to begin operations, 
 * such as advertising or scanning. Typically, GATT server initialization 
 * and advertising start from this function.
 *
 * @return None
 */
static void ble_on_sync(void) {
	int32_t rc;
	/* Make sure we have proper identity address set (public preferred) */
	rc = ble_hs_util_ensure_addr(0);
	assert(rc == 0);

	/* Figure out address to use while advertising (no privacy for now) */
	rc = ble_hs_id_infer_auto(0, &own_addr_type);
	if (rc != 0)
	{
		MODLOG_DFLT(ERROR, "error determining address type; rc=%ld\n", rc);
		return;
	}

	/* Printing ADDR */
	uint8_t addr_val[6] = {0};
	rc = ble_hs_id_copy_addr(own_addr_type, addr_val, NULL);

	MODLOG_DFLT(INFO, "Device Address: ");
	print_addr(addr_val);
	MODLOG_DFLT(INFO, "\n");

	/* Set BLE as synced */
	ble_is_synced = true;

}

/**
 * @brief Initializes BLE security configuration.
 *
 * This function configures the BLE security settings, including
 * bonding, authentication requirements, I/O capabilities, and
 * key distribution. It ensures that BLE connections are secure
 * with the desired pairing and bonding modes.
 *
 * @note This should be called after BLE stack initialization and
 * before starting advertising or accepting connections.
 *
 * @return None.
 */
static void security_initialization(void) {
    // Configure I/O capabilities (NoInputNoOutput = 3)
    ble_hs_cfg.sm_io_cap = 3;

    // Disable BLE bonding - we'll use address verification instead
    ble_hs_cfg.sm_bonding = 0;

    // Configure MITM protection (disabled for NoInputNoOutput)
    ble_hs_cfg.sm_mitm = 0;

    // Disable Secure Connections since we're not using bonding
    ble_hs_cfg.sm_sc = 0;

    ESP_LOGI(tag, "BLE Security initialized - Address verification mode");
}

/**
 * @brief Initializes the BLE stack and security configuration.
 *
 * This function initializes the BLE stack, configures security settings (bonding, 
 * authentication, I/O capabilities, and key distribution), and ensures the device 
 * is ready for advertising or connections. It must be called after hardware 
 * initialization but before any BLE operations (advertising/scanning/connecting).
 *
 * @note This function may depend on other BLE stack initialization steps.
 *       Ensure dependencies (e.g., SoftDevice initialization on Nordic platforms) 
 *       are resolved before calling.
 *
 * @return status
 */
int32_t ble_initialization(void) {
    int32_t ret;

	/* Initialize the NimBLE host configuration. */
	ble_hs_cfg.reset_cb = ble_on_reset;
	ble_hs_cfg.sync_cb = ble_on_sync;
	ble_hs_cfg.gatts_register_cb = gatt_svr_register_cb;
	ble_hs_cfg.store_status_cb = ble_store_util_status_rr;

	security_initialization();

	ret = gatt_svr_init();
	assert(ret == 0);

	/* Set the default device name. */
	ret = ble_svc_gap_device_name_set("MR Car 7");
	assert(ret == 0);

	/* XXX Need to have template for store */
	ble_store_config_init();

    return ret;
}





/**
 * @brief Gets the state of the stop_adv_on_disconnect_flag.
 *
 * @return True if advertising should stop on disconnect, false otherwise.
 */
bool get_stop_adv_on_disconnect_flag(void) {
    return stop_adv_on_disconnect_flag;
}

/**
 * @brief Sets the state of the stop_adv_on_disconnect_flag.
 *
 * @param state True to set the flag, false to clear it.
 */
void set_stop_adv_on_disconnect_flag(bool state) {
    stop_adv_on_disconnect_flag = state;
}

/**
 * @brief Updates BLE device name and starts advertising with the new name.
 *
 * This function updates the BLE device name and restarts advertising to
 * broadcast the new name. It handles the complete process of name update
 * and advertising restart.
 *
 * @param new_name   Pointer to the new BLE device name string.
 *                   Must be null-terminated and <= 31 characters.
 *
 * @return  0 on success,
 *          non-zero error code if name update or advertising fails.
 */
int update_ble_name_and_start_advertising(const char* new_name)
{
    ESP_LOGI(tag, "BLE device name rec: '%s'", new_name);

    if (!new_name) {
        ESP_LOGE(tag, "BLE name cannot be NULL");
        return -1;
    }

    size_t name_len = strlen(new_name);
    if (name_len == 0 || name_len > 31) {
        ESP_LOGE(tag, "Invalid BLE name length: %zu (must be 1-31 chars)", name_len);
        return -2;
    }

    // Stop current advertising if active
    if (ble_gap_adv_active()) {
        ble_advertisement_stop();
        vTaskDelay(pdMS_TO_TICKS(100)); // Small delay to ensure advertising stops
    }

    // Update the BLE device name
    int rc = ble_svc_gap_device_name_set(new_name);
    if (rc != 0) {
        ESP_LOGE(tag, "Failed to set BLE name to '%s', rc=%d", new_name, rc);
        return rc;
    }

    ESP_LOGI(tag, "BLE device name updated to: '%s'", new_name);

    // Start advertising with the new name
    if (is_ble_synced()) {
        ble_advertisement_start();
        ESP_LOGI(tag, "BLE advertising started with new name: '%s'", new_name);
        return 0;
    } else {
        ESP_LOGW(tag, "BLE not synced, cannot start advertising");
        return -3;
    }
}

/**
 * @brief Gets the current BLE device name.
 *
 * This function retrieves the currently configured BLE device name
 * from the BLE stack.
 *
 * @return  Pointer to the current BLE device name string,
 *          or NULL if name retrieval fails.
 */
const char* get_current_ble_name(void)
{
    return ble_svc_gap_device_name();
}

/**
 * @brief Starts BLE advertising.
 *
 * This function initiates the BLE advertising process, making the
 * device discoverable to other BLE devices. It is typically called
 * when the application needs to start advertising, for example,
 * on startup or after a disconnection.
 *
 * @note This function should be called only when the BLE stack is
 * synchronized and ready for operations.
 *
 * @return None
 */
void ble_advertisement_start(void)
{
	if (is_ble_synced() && !ble_gap_adv_active())
	{
		led_set_ble_status(LED_STATE_BLINK_FAST);  // Blue LED: Blink Fast for Advertising

		ESP_LOGI(tag, "Starting BLE advertising...");
		ble_svc_gap_device_name_set("CT CAR 2");
		ble_advertisement();
		fflush(stdout);
	}
	else
	{
		if (!is_ble_synced())
		{
			ESP_LOGW(tag, "Cannot start advertising: BLE not synced");
		}
		if (ble_gap_adv_active())
		{
			ESP_LOGW(tag, "Cannot start advertising: Already advertising");
		}
	}
}

/**
 * @brief Stops BLE advertising.
 *
 * This function stops the ongoing BLE advertising process, making
 * the device no longer discoverable. It is typically called when
 * the application needs to stop advertising, for example, on
 * entering a low-power mode or when a connection is established.
 *
 * @note This function should be called only when advertising is
 * active.
 *
 * @return None
 */
void ble_advertisement_stop(void)
{
	if (ble_gap_adv_active())
	{
		led_set_ble_status(LED_STATE_OFF);  // OFF Blue LED NO Advertising
		ESP_LOGI(tag, "Stopping BLE advertising...");
		ble_gap_adv_stop();
		fflush(stdout);
	}
	else
	{
		ESP_LOGI(tag, "BLE advertising is not active.");
	}
}

/**
 * @brief Checks if BLE stack is synchronized.
 *
 * This function returns the current synchronization state of the BLE stack.
 *
 * @return True if BLE is synced and ready for operations, false otherwise
 */
bool is_ble_synced(void)
{
    return ble_is_synced;
}

/**
 * @brief Stores the connected device's Bluetooth address to NVS.
 *
 * This function saves the Bluetooth address of the connected device to
 * Non-Volatile Storage (NVS) for device verification on future connections.
 *
 * @param addr Pointer to the BLE address structure to store
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t store_device_address_to_nvs(const ble_addr_t *addr)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    if (addr == NULL) {
        ESP_LOGE(tag, "Invalid address pointer");
        return ESP_ERR_INVALID_ARG;
    }

    err = nvs_open("storage", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(tag, "Error (%s) opening NVS handle for device address!", esp_err_to_name(err));
        return err;
    }

    // Store the complete BLE address structure (type + address)
    err = nvs_set_blob(nvs_handle, "device_addr", addr, sizeof(ble_addr_t));
    if (err != ESP_OK) {
        ESP_LOGE(tag, "Error (%s) writing device address to NVS!", esp_err_to_name(err));
    } else {
        ESP_LOGI(tag, "Device address stored to NVS: %02x:%02x:%02x:%02x:%02x:%02x (type: %d)",
                 addr->val[5], addr->val[4], addr->val[3],
                 addr->val[2], addr->val[1], addr->val[0], addr->type);
    }

    nvs_close(nvs_handle);
    return err;
}

/**
 * @brief Loads the stored device address from NVS.
 *
 * This function retrieves the previously stored Bluetooth address from NVS
 * for device verification purposes.
 *
 * @param addr Pointer to BLE address structure to fill with stored address
 *
 * @return ESP_OK if address found and loaded, ESP_ERR_NVS_NOT_FOUND if no address stored, other error codes on failure
 */
esp_err_t load_device_address_from_nvs(ble_addr_t *addr)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;
    size_t required_size = sizeof(ble_addr_t);

    if (addr == NULL) {
        ESP_LOGE(tag, "Invalid address pointer");
        return ESP_ERR_INVALID_ARG;
    }

    err = nvs_open("storage", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(tag, "Error (%s) opening NVS handle for device address!", esp_err_to_name(err));
        return err;
    }

    err = nvs_get_blob(nvs_handle, "device_addr", addr, &required_size);
    if (err == ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGI(tag, "No device address found in NVS - first time connection");
    } else if (err != ESP_OK) {
        ESP_LOGE(tag, "Error (%s) reading device address from NVS!", esp_err_to_name(err));
    } else {
        ESP_LOGI(tag, "Device address loaded from NVS: %02x:%02x:%02x:%02x:%02x:%02x (type: %d)",
                 addr->val[5], addr->val[4], addr->val[3],
                 addr->val[2], addr->val[1], addr->val[0], addr->type);
    }

    nvs_close(nvs_handle);
    return err;
}

/**
 * @brief Clears the stored device address from NVS.
 *
 * This function removes the stored device address from NVS, effectively
 * resetting the device verification system.
 *
 * @return ESP_OK on success, error code on failure
 */
esp_err_t clear_device_address_from_nvs(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    err = nvs_open("storage", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(tag, "Error (%s) opening NVS handle for device address!", esp_err_to_name(err));
        return err;
    }

    err = nvs_erase_key(nvs_handle, "device_addr");
    if (err == ESP_ERR_NVS_NOT_FOUND) {
        ESP_LOGI(tag, "Device address not found in NVS - already cleared");
        err = ESP_OK; // Not an error if already cleared
    } else if (err != ESP_OK) {
        ESP_LOGE(tag, "Error (%s) clearing device address from NVS!", esp_err_to_name(err));
    } else {
        ESP_LOGI(tag, "Device address cleared from NVS");
    }

    nvs_close(nvs_handle);
    return err;
}

/**
 * @brief Compares two BLE addresses for equality.
 *
 * This function compares two BLE address structures to determine if they
 * represent the same device.
 *
 * @param addr1 Pointer to first BLE address
 * @param addr2 Pointer to second BLE address
 *
 * @return True if addresses are identical, false otherwise
 */
bool compare_ble_addresses(const ble_addr_t *addr1, const ble_addr_t *addr2)
{
    if (addr1 == NULL || addr2 == NULL) {
        return false;
    }

    // Compare address type and all 6 bytes of the address
    if (addr1->type != addr2->type) {
        return false;
    }

    return (memcmp(addr1->val, addr2->val, 6) == 0);
}


