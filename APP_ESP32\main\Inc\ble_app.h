/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_app.h
* @version        : 1.0.2
* @brief          : Header for ble_app.c file
* @details        : Header for ble_app.c file
********************************************************************************
* @version 1.0.1                                         				Date : 21/07/2025
* [+] Added function declarations for BLE advertising control via UART
* [+] Added ble_advertisement_start(), ble_advertisement_stop(), is_ble_synced()
* [+] Added clear_state_from_nvs() declaration for NVS state management (later removed)
********************************************************************************
* @version 1.0.2                                         				Date : 24/07/2025
* [+] Added BLE bonding state management functions for NVS persistence
* [+] Added set_bonding_state_to_nvs() - saves bonding state to NVS
* [+] Added clear_bonding_state_from_nvs() - clears bonding state and returns previous state
* [+] Added load_bonding_state_from_nvs() - loads bonding state from NVS
* [-] Removed BLE advertising state NVS functions (set_state_to_nvs, clear_state_from_nvs)
********************************************************************************/
#ifndef BLE_APP_H
#define BLE_APP_H

#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

int32_t ble_initialization(void);
bool get_stop_adv_on_disconnect_flag(void);
void set_stop_adv_on_disconnect_flag(bool state);
uint16_t get_connection_handle(void);
int update_ble_name_and_start_advertising(const char* new_name);
const char* get_current_ble_name(void);
void ble_advertisement_start(void);
void ble_advertisement_stop(void);
bool is_ble_synced(void);
void set_bonding_state_to_nvs(bool state);
bool clear_bonding_state_from_nvs(void);
bool load_bonding_state_from_nvs(void);


#ifdef __cplusplus
}
#endif

#endif /* END OF BLE_APP_H */