/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_app.h
* @version        : 1.0.5
* @brief          : Header for ble_app.c file
* @details        : Header for ble_app.c file
********************************************************************************
* @version 1.0.1                                         				Date : 21/07/2025
* [+] Added function declarations for BLE advertising control via UART
* [+] Added ble_advertisement_start(), ble_advertisement_stop(), is_ble_synced()
* [+] Added clear_state_from_nvs() declaration for NVS state management (later removed)
********************************************************************************
* @version 1.0.2                                         				Date : 24/07/2025
* [+] Added BLE bonding state management functions for NVS persistence
* [+] Added set_bonding_state_to_nvs() - saves bonding state to NVS
* [+] Added clear_bonding_state_from_nvs() - clears bonding state and returns previous state
* [+] Added load_bonding_state_from_nvs() - loads bonding state from NVS
* [-] Removed BLE advertising state NVS functions (set_state_to_nvs, clear_state_from_nvs)
********************************************************************************
* @version 1.0.3                                         				Date : 26/07/2025
* [+] Added complete BLE bonding implementation with UART control
* [+] Added enable_ble_bonding() - enables bonding and updates security config
* [+] Added disable_ble_bonding() - disables bonding and updates security config
* [+] Added clear_all_bonding_info() - clears all bonding data from NVS and BLE stack
* [+] Added get_bonding_status() - returns current bonding status and bonded device count
* [+] Enhanced security_initialization() to load bonding state from NVS
* [+] Added UART commands: BLE_BOND_ENABLE, BLE_BOND_DISABLE, BLE_BOND_CLEAR, BLE_BOND_STATUS
********************************************************************************
* @version 1.0.4                                         				Date : 26/07/2025
* [~] Simplified BLE bonding implementation - always enabled for mobile devices
* [-] Removed UART control commands for bonding (BLE_BOND_ENABLE, BLE_BOND_DISABLE, etc.)
* [-] Removed enable_ble_bonding(), disable_ble_bonding(), clear_all_bonding_info() functions
* [-] Removed get_bonding_status() function
* [~] Modified security_initialization() to always enable BLE bonding and Secure Connections
* [+] BLE bonding now permanently enabled for ESP32 ↔ Mobile device connections
********************************************************************************
* @version 1.0.5                                         				Date : 26/07/2025
* [-] Removed BLE bonding logic completely - replaced with device address verification
* [+] Added device address verification system using NVS storage
* [+] Added store_device_address_to_nvs() - stores connected device's BLE address
* [+] Added load_device_address_from_nvs() - loads stored device address for verification
* [+] Added clear_device_address_from_nvs() - clears stored device address
* [+] Added compare_ble_addresses() - compares two BLE addresses for equality
* [~] Modified BLE_GAP_EVENT_LINK_ESTAB to verify device identity on connection
* [~] Disabled BLE bonding (sm_bonding = 0, sm_sc = 0) - using address verification instead
********************************************************************************/
#ifndef BLE_APP_H
#define BLE_APP_H

#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

int32_t ble_initialization(void);
bool get_stop_adv_on_disconnect_flag(void);
void set_stop_adv_on_disconnect_flag(bool state);
uint16_t get_connection_handle(void);
int update_ble_name_and_start_advertising(const char* new_name);
const char* get_current_ble_name(void);
void ble_advertisement_start(void);
void ble_advertisement_stop(void);
bool is_ble_synced(void);

// Device address verification functions
esp_err_t store_device_address_to_nvs(const ble_addr_t *addr);
esp_err_t load_device_address_from_nvs(ble_addr_t *addr);
esp_err_t clear_device_address_from_nvs(void);
bool compare_ble_addresses(const ble_addr_t *addr1, const ble_addr_t *addr2);



#ifdef __cplusplus
}
#endif

#endif /* END OF BLE_APP_H */