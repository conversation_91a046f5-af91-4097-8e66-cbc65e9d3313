/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : main.c
* @version        : 1.0.4
* @brief          : Main File
* @details        : Main File
********************************************************************************
* @version 1.0.1                                         				Date : 18/07/2025
* [~] Moved BLE moved to ble_app.c
********************************************************************************
* @version 1.0.2                                         				Date : 21/07/2025
* [+] Added NVS state loading for BLE advertising persistence across reboots (later removed)
* [+] Added clear_state_from_nvs() call in app_main() for state restoration (later removed)
********************************************************************************
* @version 1.0.3                                         				Date : 24/07/2025
* [+] Added Timer Initalization
********************************************************************************
* @version 1.0.4                                         				Date : 25/07/2025
* [-] Removed NVS for BLE Advertisment
********************************************************************************/

#include "esp_log.h"
#include "nvs_flash.h"
#include "uart_handler.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

#include "crc.h"
#include "ble_app.h"
#include "flash_handler.h"
#include "fw_file_handler.h"
#include "timer_handler.h"
#include "led_control.h"

#define BLE_TASK_STACK_SIZE (4 * 1024)
#define FW_XFER_TASK_STACK_SIZE (4 * 1024)
#define UART_TASK_STACK_SIZE (10 * 1024)

extern QueueHandle_t fw_file_data_queue;
TaskHandle_t fw_file_write_handle;
TaskHandle_t fw_file_xfer_handle;


static StackType_t ble_task_stack[BLE_TASK_STACK_SIZE];
static StackType_t uart_task_stack[UART_TASK_STACK_SIZE];
static StackType_t fw_xfer_task_stack[FW_XFER_TASK_STACK_SIZE];

static StaticTask_t ble_task_tcb;
static StaticTask_t uart_task_tcb;
static StaticTask_t fw_xfer_task_tcb;

/**
 * @brief Host task for NimBLE stack execution.
 *
 * This FreeRTOS task runs the  host stack event loop. 
 * It is responsible for processing BLE events such as connections, 
 * disconnections, GATT operations, and GAP events.
 *
 * This task typically runs indefinitely and should be created 
 * during BLE initialization.
 *
 * @param param   Optional parameter passed to the task (usually NULL).
 *
 * @return None. This function never returns under normal operation.
 */
void host_task(void *param) {
	/* This function will return only when nimble_port_stop() is executed */
	nimble_port_run();
	nimble_port_freertos_deinit();
}

/**
 * @brief Main application entry point.
 *
 * This is the main entry function for the application. It initializes
 * system peripherals, BLE stack, UART, security configurations, GATT
 * server, and starts BLE advertising. It also creates necessary tasks
 * like BLE host task and UART event handling task.
 *
 * @note This function is automatically called by the ESP-IDF framework
 * after startup. It should never return.
 *
 * @return None.
 */
void app_main(void) {
	/* Initialize NVS — it is used to store PHY calibration data */
	esp_err_t ret = nvs_flash_init();
	if (ret == ESP_ERR_NVS_NO_FREE_PAGES)
	{
		ESP_ERROR_CHECK(nvs_flash_erase());
		ret = nvs_flash_init();
	}
	ESP_ERROR_CHECK(ret);
	ESP_ERROR_CHECK(nimble_port_init());

	ESP_ERROR_CHECK(ble_initialization());
	ESP_ERROR_CHECK(uart_initialization());
	ESP_ERROR_CHECK(flash_initalization());
	ESP_ERROR_CHECK(timer_initalization());
	crc32_init();

	xTaskCreateStatic(uart_event_task, "uart_event_task", UART_TASK_STACK_SIZE, NULL, 3, uart_task_stack, &uart_task_tcb);
	ESP_LOGI("HEAP", "Free heap: %ld", esp_get_free_heap_size());
	fw_file_write_handle = xTaskCreateStaticPinnedToCore(ble_data_write_task, "ble_data_write_task", BLE_TASK_STACK_SIZE, NULL, 5, ble_task_stack, &ble_task_tcb, 1);
	fw_file_xfer_handle = xTaskCreateStaticPinnedToCore(write_data_to_nexus, "write_data_to_nexus", FW_XFER_TASK_STACK_SIZE, NULL, 5, fw_xfer_task_stack, &fw_xfer_task_tcb, 1);
	
	nimble_port_freertos_init(host_task);

	/* LED init */
    led_init_all();      // Initialize GPIOs for all 4 LEDs
    led_start_tasks();   // Start FreeRTOS tasks to manage LED behavior

}
	
