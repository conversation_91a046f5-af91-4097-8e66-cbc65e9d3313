#!/usr/bin/env python3
"""
BLE Device Address Verification Test Script
Tests ESP32 device address verification functionality
"""

import asyncio
import sys
from bleak import BleakScanner, BleakClient

async def test_esp32_bonding():
    print("🔍 Scanning for ESP32 devices...")
    
    # Scan for devices
    devices = await BleakScanner.discover(timeout=10.0)
    
    # Find ESP32 (adjust name if needed)
    esp32_device = None
    for device in devices:
        if device.name and ("ESP32" in device.name or "esp32" in device.name.lower()):
            esp32_device = device
            break
    
    if not esp32_device:
        print("❌ ESP32 device not found!")
        print("📋 Available devices:")
        for device in devices:
            print(f"   - {device.name} ({device.address})")
        return False
    
    print(f"✅ Found ESP32: {esp32_device.name} - {esp32_device.address}")
    
    try:
        print("🔗 Connecting to ESP32...")
        async with BleakClient(esp32_device.address) as client:
            if client.is_connected:
                print("✅ Connected successfully!")
                print("📝 ESP32 should store your device's Bluetooth address")

                # Keep connection for a few seconds
                await asyncio.sleep(3)

                print("📋 Connection info:")
                print(f"   - Device: {esp32_device.name}")
                print(f"   - Address: {esp32_device.address}")
                print(f"   - Connected: {client.is_connected}")

                print("✅ Test completed successfully!")
                print("📱 ESP32 should have stored your device address for future verification")
                return True
            else:
                print("❌ Connection failed")
                return False
                
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

async def main():
    print("🧪 ESP32 Device Address Verification Test")
    print("=" * 50)

    success = await test_esp32_bonding()

    print("\n" + "=" * 50)
    if success:
        print("🎉 Device address verification test PASSED!")
        print("\n📋 Next steps:")
        print("1. Check ESP32 logs for address storage")
        print("2. Try disconnecting and reconnecting")
        print("3. ESP32 should verify your device address")
        print("4. Try connecting with a different device")
        print("5. ESP32 should detect different device")
    else:
        print("❌ Device address verification test FAILED!")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure ESP32 is flashed and running")
        print("2. Check ESP32 logs for BLE advertising")
        print("3. Verify ESP32 device name in code")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except ImportError:
        print("❌ Error: 'bleak' library not installed")
        print("📦 Install with: pip install bleak")
        sys.exit(1)
