{"write_flash_args": ["--flash_mode", "dio", "--flash_size", "detect", "--flash_freq", "80m"], "flash_settings": {"flash_mode": "dio", "flash_size": "detect", "flash_freq": "80m"}, "flash_files": {"0x0": "bootloader/bootloader.bin", "0x10000": "Vantage_nxESP32_Int_Rel_1_2_1_0.bin", "0x8000": "partition_table/partition-table.bin", "0xd000": "ota_data_initial.bin"}, "bootloader": {"offset": "0x0", "file": "bootloader/bootloader.bin", "encrypted": "false"}, "app": {"offset": "0x10000", "file": "Vantage_nxESP32_Int_Rel_1_2_1_0.bin", "encrypted": "false"}, "partition-table": {"offset": "0x8000", "file": "partition_table/partition-table.bin", "encrypted": "false"}, "otadata": {"offset": "0xd000", "file": "ota_data_initial.bin", "encrypted": "false"}, "extra_esptool_args": {"after": "hard_reset", "before": "default_reset", "stub": true, "chip": "esp32s3"}}