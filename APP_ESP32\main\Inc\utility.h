/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : utility.h
* @version        : 1.0.3
* @brief          : Header for utility.c file
* @details        : Header for utility.c file
********************************************************************************
* @version 1.0.1                                         				Date : 09/07/2025
* [+] Added Write config Request and Response
********************************************************************************
* @version 1.0.2                                         				Date : 18/07/2025
* [+] Added File corruption macros
********************************************************************************
* @version 1.0.3                                         				Date : 24/07/2025
* [~] Modified param name for better understanding
********************************************************************************/

#ifndef UTILS_H
#define UTILS_H

#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

#define DEBUG_CMD

#define BLE_MTU 					(517)
#define START_BYTE_INDEX            (0)
#define PACKET_TYPE_INDEX           (1)
#define LENGTH_START_INDEX          (2)
#define CRC_LENGTH                  (4)
#define HEADER_SIZE                 (6) /* START BYTE-1; PACKET TYPE-1; LENGTH-4 */

uint32_t byte_array_to_u32_little_endian(const uint8_t* data);
uint32_t byte_array_to_u32_big_endian(const uint8_t* data);
void u32_to_byte_array_little_endian(uint8_t* data, uint32_t value);
void u32_to_byte_array_big_endian(uint8_t* data, uint32_t value);

/* ENUMS */
typedef enum {
    BLE_ACK = 0x01,
    BLE_NACK  = 0x02,
    BLE_START_ADV = 0x10,
    BLE_STOP_ADV = 0x11,
    BLE_CONNECTED  = 0x12,
    BLE_DISCONNECTED = 0x13,
    BLE_BOND_ENABLE = 0x14,
    BLE_BOND_DISABLE = 0x15,
    BLE_BOND_CLEAR = 0x16,
    BLE_BOND_STATUS = 0x17,
    BLE_FW_UPLOAD_START = 0x20,
    BLE_FW_UPLOAD_DATA = 0x21,
    BLE_FW_UPLOAD_END = 0x22,
    BLE_FW_UPLOAD_SUCCESS = 0x23,
    BLE_FW_UPLOAD_FAIL = 0x24,
    BLE_FW_CHUNK_START = 0x30,
    BLE_FW_CHUNK_DATA = 0x31,
    BLE_FW_CHUNK_END = 0x32,
    BLE_FW_PROGRESS = 0x40,
    BLE_READ_CONFIG_REQUEST = 0x50,
    BLE_READ_CONFIG_RESPONSE = 0x51,
    BLE_VERSION_REQUEST = 0x52,
    BLE_VERSION_RESPONSE = 0x53,
    BLE_FW_NODE_SEL_REQUEST = 0x54,
    BLE_FW_NODE_SEL_RESPONSE = 0x55,
    BLE_WRITE_CONFIG_REQUEST = 0x56,
    BLE_WRITE_CONFIG_RESPONSE = 0x57,
    BLE_PKT_ERROR = 0x60,
    START_BYTE = 0xA5
}packet_type_t;

typedef enum {
    SUCCESS = 0x00,
    BLE_ACK_SUCCESS = 0x01,
    BLE_NACK_FAIL = 0x02,  
    START_BYTE_ERROR = -1,
    INVALID_PACKET_ERROR = -2, 
    CRC_CHECK_ERROR = -3,
    UART_TX_ERROR = -4,
    UART_RX_ERROR = -5,
    REQUEST_PACKET_NOT_RECEIVED = -6,
    NULL_DATA_RECEIVED = -7,
    INVALID_LEN = -8,
    FILE_CORRUPTION_ERROR = -9,
    UNKNOWN_ERROR
}error_code_t;

typedef enum {
    HALF_0_READY = 0,
    HALF_1_READY = 1,
    FINAL_FLUSH  = 2,  // New flag for last partial write
    FW_CHUNK_START = 3,
    FW_CHUNK_DATA = 4,
    FW_CHUNK_END = 5,
} RTOSNotifyIndex;

#ifdef __cplusplus
}
#endif

#endif /* END OF UTILS_H */
