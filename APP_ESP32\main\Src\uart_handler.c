/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : uart_handler.c
* @version        : 1.0.7
* @brief          : Handle the UART functionality
* @details        : Handle the UART functionality
********************************************************************************
* @version 1.0.1                                         				Date : 09/07/2025
* [+] Handles the write configuration file received from mobile app
* [~] Modified BAUD RATE to 1M
********************************************************************************
* @version 1.0.2                                         				Date : 18/07/2025
* [+] Handles the write configuration file received from mobile app
* [+] Added return to UART initialization
********************************************************************************
* @version 1.0.3                                         				Date : 21/07/2025
* [+] Added BLE_START_ADV and BLE_STOP_ADV command handling via UART
* [+] Implemented dynamic BLE advertising name update functionality
* [+] Added NVS state persistence for BLE advertising across reboots
********************************************************************************
* @version 1.0.4                                         				Date : 21/07/2025
* [-] Removed unused send_uart_command_with_timeout() function
* [~] Code cleanup and optimization
********************************************************************************
* @version 1.0.5                                         				Date : 23/07/2025
* [~] Added temporary BLE_START_ADV implementation with hardcoded BLE name ("MR Car 7")
*     until MR board sends the Nexus Node Name dynamically
*     Preserved dynamic name logic in disabled block for future reactivation
* [~] Uart tested on 2 Mbps speed (Baud: 2000000), as compatible to MR Board.
********************************************************************************
* @version 1.0.6                                         				Date : 24/07/2025
* [+] Added BLE_ACK and BLE_NACK Handler
* [+] Added Timer
********************************************************************************
* @version 1.0.7                                         				Date : 25/07/2025
* [+] UART FLUSH during uart break
* [~] UART moving back to start state
********************************************************************************/
#include <stdint.h>
#include <string.h>
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "uart_handler.h"
#include "crc.h"
#include "utility.h"
#include "ble_response_handler.h"
#include "ble_app.h"
#include "led_control.h"

static const char *TAG = "UART";
#define UART_PORT UART_NUM_1

// In your header file or at top of C file

#define RD_BUF_SIZE (1024)

#define TXD_PIN ((gpio_num_t)GPIO_NUM_17)
#define RXD_PIN ((gpio_num_t)GPIO_NUM_18)
#define RTS_PIN ((gpio_num_t)GPIO_NUM_19)
#define CTS_PIN ((gpio_num_t)GPIO_NUM_20)

#define BAUD_RATE (2000000) 

static uart_write_state_t uart_state = UART_READ_START;
extern volatile bool ack_flag;
extern volatile bool nack_flag;
extern esp_timer_handle_t timer_handle;
int32_t uart_read_index = 0;

/* FUNCTION PROTOTYPE */
static void uart_read_state_machine(uint8_t *data, uint32_t len);
void initialize_uart_vars(void);
static void send_ack_nack(uint8_t ack_nack);

extern void notify_client(uint16_t conn_handle, uint16_t attr_handle, uint8_t err_code);

char dtmp[RD_BUF_SIZE];
static QueueHandle_t uart1_queue;

/**
 * @brief Initializes the UART peripheral.
 *
 * This function configures and initializes the UART interface with
 * predefined settings such as baud rate, data bits, stop bits, parity,
 * and flow control. It prepares the UART for transmission and reception
 * of data.
 *
 * @note This function must be called before any UART send or receive
 * operations.
 *
 * @return None.
 */
esp_err_t uart_initialization(void)
{
    esp_err_t ret;
    uart_config_t uart_config = {
        .baud_rate = BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    // Install UART driver, and get the queue.
    ret = uart_driver_install(UART_PORT, RD_BUF_SIZE * 15, RD_BUF_SIZE * 2, 80, &uart1_queue, 0);
    if (ret != ESP_OK) return ret;
    // CONFIGURE UART 
    ret = uart_param_config(UART_PORT, &uart_config);
    if (ret != ESP_OK) return ret;

    // Set UART pins (using UART0 default pins ie no changes.)
    ret = uart_set_pin(UART_PORT, TXD_PIN, RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) return ret;
    return ret;
}

/**
 * @brief Resets the variables used in UART.
 *
 * @param None
 *
 * @return None.
 */
void initialize_uart_vars(void)
{
	uart_state = UART_READ_START;
	uart_read_index = 0;
}

/**
 * @brief UART write state machine to transmit data over UART.
 *
 * This function manages the UART transmission process using a state
 * machine approach. It handles chunked sending of large data buffers,
 * manages transmission states, and ensures proper sequencing of UART
 * data transfers.
 *
 * @param data   Pointer to the data buffer to transmit.
 * @param len    Length of the data in bytes.
 *
 * @return None.
 */
static void uart_read_state_machine(uint8_t *data, uint32_t len)
{
	error_code_t status = SUCCESS;
	uint32_t crc_calc = 0;

	switch (uart_state)
	{
	case UART_READ_START:
		if (data[0] != START_BYTE)
		{
#ifdef DEBUG_CMD
			ESP_LOGI("UART_READ: ", "ERROR");
#endif
			printf("0x");
			for (size_t i = 0; i < len; i++) {
				printf("%X", data[i]);
			}
			printf("\n");
			status = START_BYTE_ERROR;
			// uart_write_bytes(UART_PORT, &status, 1);
			uart_read_index = 0;
			led_set_error(true, false);  // blink red on UART errors
			break;
		}
		switch (data[1])
		{
		case BLE_READ_CONFIG_RESPONSE:
		case BLE_VERSION_RESPONSE:
			set_data_read_length(byte_array_to_u32_little_endian(&data[LENGTH_START_INDEX]));
			reset_buffer_variables();
			store_config_file(&data[HEADER_SIZE], (len - HEADER_SIZE));
			uart_state = UART_READ_DATA;
			/* TODO: CAN BE IMPLEMENTED ONCE IMPLEMENTATION DONE IN NEXUS*/
			break;
		case BLE_ACK:
			crc_calc = byte_array_to_u32_little_endian(&data[HEADER_SIZE]);
			if (validate_crc(data, HEADER_SIZE, crc_calc)) {
				ESP_LOGI("UART: ", "Valid ACK");
				ack_flag = true;
			}
			else {
				ESP_LOGE("UART: ", "inValid ACK");
			}
			break;
		case BLE_NACK:
			crc_calc = byte_array_to_u32_little_endian(&data[HEADER_SIZE]);
			if (validate_crc(data, HEADER_SIZE, crc_calc)) {
				ESP_LOGI("UART: ", "Valid NACK");
				nack_flag = true;
			}
			else {
				ESP_LOGE("UART: ", "inValid NACK");
			}
			break;
		case BLE_START_ADV:
			{
				uint32_t payload_len = byte_array_to_u32_little_endian(&data[2]);
				 reset_buffer_variables();
				if (payload_len > 0 && payload_len <= 31) {
					// Extract BLE name from payload
					char ble_name[32] = {0}; // 31 chars + null terminator
					memcpy(ble_name, &data[HEADER_SIZE], payload_len);
					ble_name[payload_len] = '\0'; // Ensure null termination

					ESP_LOGI(TAG, "Received BLE_START_ADV with name: '%s'", ble_name);

					// Update BLE name and start advertising
					int result = update_ble_name_and_start_advertising(ble_name);
					if (result == 0) {
						send_ack_nack(BLE_ACK);
						ESP_LOGI(TAG, "BLE advertising started successfully");
					} else {
						send_ack_nack(BLE_NACK);
						ESP_LOGE(TAG, "Failed to start BLE advertising, error: %d", result);
					}
				} else {
					ESP_LOGE(TAG, "Invalid BLE name length: %ld", payload_len);
					send_ack_nack(BLE_NACK);
				}
				uart_state = UART_READ_START;
			}
			break;
		case BLE_STOP_ADV:
			{
				ESP_LOGI(TAG, "Received BLE_STOP_ADV command");
				// Stop BLE advertising
				ble_advertisement_stop();
				send_ack_nack(BLE_ACK);
				ESP_LOGI(TAG, "BLE advertising stopped successfully");

				uart_state = UART_READ_START;
			}
			break;

		default:
			status = INVALID_PACKET_ERROR;
			uart_write_bytes(UART_PORT, &status, 1);
			uart_state = UART_READ_START;
			break;
		}
		break;
	case UART_READ_DATA:
		store_config_file(data, len);
		break;
	default:
#ifdef DEBUG_CMD
		ESP_LOGI("UART_READ: : ", "DEFAULT");
#endif
		break;
	}
}

/**
 * @brief UART event handling task.
 *
 * This FreeRTOS task waits for UART events from the UART driver event queue,
 * such as data reception, buffer overflows, framing errors, or pattern detection.
 * It processes incoming UART data and handles error conditions as needed.
 *
 * @param pvParameters   Pointer to task parameters (typically NULL or
 *                       a UART configuration/context structure).
 *
 * @return None. This task runs indefinitely.
 */
void uart_event_task(void *pvParameters)
{
	uart_event_t event;
	size_t buffered_size;
	bzero(dtmp, RD_BUF_SIZE);
	for (;;)
	{
		// Waiting for UART event.
		if (xQueueReceive(uart1_queue, (void *)&event, (TickType_t)portMAX_DELAY))
		{
			switch (event.type)
			{
			/* UART DATA EVENT */
			case UART_DATA:
				memset(dtmp, '\0', sizeof(dtmp));
				uart_read_bytes(UART_PORT, dtmp, event.size, (TickType_t)portMAX_DELAY);
				uart_read_state_machine((uint8_t *)dtmp, event.size);
				uart_read_index += event.size;
				uart_get_buffered_data_len(UART_PORT, &buffered_size);
				led_uart_activity(); // amber led control: Indicate UART activity with Amber LED
				led_set_error(false, false);  // red OFF on UART resume
#ifdef DEBUG_CMD
				ESP_LOGI("UART READ", "[LEN: ]: %ld", uart_read_index);
#endif
				break;
			/* UART DATA BREAK EVENT */
			case UART_DATA_BREAK:
				uart_flush_input(UART_PORT);
				uart_state = UART_READ_START;
#ifdef DEBUG_CMD
				ESP_LOGI(TAG, "UART_DATA_BREAK");
#endif
				break;
			case UART_PATTERN_DET:
				uart_flush_input(UART_PORT);
				break;
			/* UART FIFO OVERFLOW */
			case UART_FIFO_OVF:
#ifdef DEBUG_CMD
				ESP_LOGI(TAG, "UART_FIFO_OVF");
#endif
				uart_flush_input(UART_PORT);
				xQueueReset(uart1_queue);
				led_set_error(true, false);  // blink red on UART errors
				uart_state = UART_READ_START;
				break;
			/* UART RING BUFFER OVERFLOW THE RX BUFFER DURING UART DRIVER INSTALL */
			case UART_BUFFER_FULL:
#ifdef DEBUG_CMD
				ESP_LOGI(TAG, "UART_BUFFER_FULL");
#endif
				uart_flush_input(UART_PORT);
				xQueueReset(uart1_queue);
			break;
			/* UART BREAK EVENT */
			case UART_BREAK:
			led_set_error(true, true);  //  solid red on UART errors
			uart_flush_input(UART_PORT);
			uart_state = UART_READ_START;
			break;
			/* UART COMMUNICATION ERROR */
			case UART_PARITY_ERR:
			case UART_FRAME_ERR:
#ifdef DEBUG_CMD
				ESP_LOGI(TAG, "uart frame error");
#endif
				led_set_error(true, false);  // blink red on UART errors
				uart_flush_input(UART_PORT);
				uart_state = UART_READ_START;
				break;
			/* OTHER EVENT APART FROM THE ABOVE EVENT */
			default:
#ifdef DEBUG_CMD
				uart_flush_input(UART_PORT);
				ESP_LOGI(TAG, "uart event type: %d", event.type);
#endif
				break;
			}
		}
	}
}

/**
 * @brief Writes data into the internal buffer.
 *
 * This function copies the provided data into the internal
 * data buffer starting from the current write offset. It handles
 * boundary checks to ensure that the buffer does not overflow.
 *
 * @param src    Pointer to the source data to be written.
 * @param size   Number of bytes to write.
 *
 * @return  0 on success,
 *         -1 if input is invalid,
 *         -2 if write exceeds buffer capacity.
 */
int32_t write_data(const void *src, uint32_t size)
{
	initialize_uart_vars();
	uart_flush_input(UART_PORT);
#ifdef DEBUG_CMD
	ESP_LOGI("write_data", "Received: write_data");
#endif
	led_uart_activity(); // Amber LED UART Comm
	return uart_write_bytes(UART_PORT, src, size);
}


/**
 * @brief Sends an ACK or NACK response over UART.
 *
 * This function constructs and sends an acknowledgment (ACK) or
 * negative acknowledgment (NACK) frame over the UART interface.
 * It is used to signal the success or failure of a received command
 * or data transmission.
 *
 * @param ack_nack   The type of response to send, either BLE_ACK
 *                   or BLE_NACK.
 *
 * @return None.
 */
static void send_ack_nack(uint8_t ack_nack)
{
	uint8_t frame[10];
	uint32_t crc;
    int tx_bytes;

    ESP_LOGI(TAG, "Attempting to send ACK/NACK: 0x%X", ack_nack);

	frame[0] = START_BYTE;
	frame[1] = ack_nack;
	frame[2] = 0;
	frame[3] = 0;
	frame[4] = 0;
	frame[5] = 0;
	crc = crc32(frame, 6);
	u32_to_byte_array_little_endian(&frame[6], crc);
	tx_bytes = write_data((const char*)frame, 10);
    ESP_LOGI(TAG, "Sent %d bytes for ACK/NACK", tx_bytes);
}



