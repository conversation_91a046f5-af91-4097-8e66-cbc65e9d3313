[1/11] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x51e0 bytes. 0x2e20 bytes (36%) free.


[2/11] No install step for 'bootloader'
[3/11] Completed 'bootloader'
[4/11] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/uart_handler.c.obj
[5/11] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/ble_app.c.obj
[6/11] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/main.c.obj
[7/11] Linking C static library esp-idf\main\libmain.a
[8/11] Generating ld/sections.ld
[9/11] Linking CXX executable Vantage_nxESP32_Int_Rel_1_2_1_0.elf
[10/11] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32s3 image...

Merged 2 ELF sections

Successfully created esp32s3 image.

Generated D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_1_0.bin
[11/11] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/partition_table/partition-table.bin D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_1_0.bin"
Vantage_nxESP32_Int_Rel_1_2_1_0.bin binary size 0x96a90 bytes. Smallest app partition is 0x100000 bytes. 0x69570 bytes (41%) free.

