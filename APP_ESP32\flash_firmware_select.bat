@echo off
REM ============================================================================
REM Universal Firmware Flash Script with Device Number Selection
REM ============================================================================
REM This script lets you select devices by pressing a number!
REM No more typing long MAC addresses!
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration
set BUILD_DIR=build
set PYTHON_SCRIPT=nimble_ota_client.py

echo ============================================================================
echo                    UNIVERSAL FIRMWARE FLASH TOOL
echo                   Select Devices by Number - Easy Mode!
echo ============================================================================
echo.

REM Check if build directory exists
if not exist "%BUILD_DIR%" (
    echo ERROR: Build directory not found: %BUILD_DIR%
    echo Please build the firmware first using: idf.py build
    pause
    exit /b 1
)

REM Find firmware files
echo Auto-detecting firmware files...
set FIRMWARE_FILE=
set FIRMWARE_COUNT=0

for %%f in ("%BUILD_DIR%\Vantage_*.bin") do (
    if exist "%%f" (
        set /a FIRMWARE_COUNT+=1
        set FIRMWARE_FILE=%%f
        set FIRMWARE_NAME=%%~nxf
        
        REM Extract version from filename
        set VERSION=%%~nxf
        set VERSION=!VERSION:Vantage_Bnry_nxESP32_FW_Int_Relse_=!
        set VERSION=!VERSION:.bin=!
        set VERSION=!VERSION:_=.!
        
        REM Get file size
        set SIZE=%%~zf
        set /a SIZE_KB=!SIZE!/1024
        
        echo   Found: %%~nxf
        echo     Version: !VERSION!
        echo     Size: !SIZE_KB! KB
        echo.
    )
)

if %FIRMWARE_COUNT% EQU 0 (
    echo ERROR: No firmware files found in %BUILD_DIR%
    echo Expected files like: Vantage_Bnry_nxESP32_FW_Int_Relse_*.bin
    echo Please build the firmware first using: idf.py build
    pause
    exit /b 1
)

echo Found %FIRMWARE_COUNT% firmware file(s)
echo Using: %FIRMWARE_NAME%

REM Extract version info
set VERSION_INFO=%FIRMWARE_NAME:Vantage_Bnry_nxESP32_FW_Int_Relse_=%
set VERSION_INFO=%VERSION_INFO:.bin=%
set VERSION_INFO=%VERSION_INFO:_=.%

REM Get file size
for %%A in ("%FIRMWARE_FILE%") do set FIRMWARE_SIZE=%%~zA
set /a FIRMWARE_SIZE_KB=%FIRMWARE_SIZE%/1024

echo Version: %VERSION_INFO%
echo Size: %FIRMWARE_SIZE% bytes (%FIRMWARE_SIZE_KB% KB)
echo.

REM Check if Python script exists
if not exist "%PYTHON_SCRIPT%" (
    echo ERROR: Python script not found: %PYTHON_SCRIPT%
    pause
    exit /b 1
)

REM Device selection with number picking
echo Device Selection:
echo 1. Scan and select by number (EASY - Recommended)
echo 2. Enter MAC address manually
echo.
set /p choice="Enter your choice (1/2): "

if "%choice%"=="1" goto scan_and_select
if "%choice%"=="2" goto manual_mac
echo Invalid choice, using scan and select mode...

:scan_and_select
echo.
echo ============================================================================
echo                           DEVICE SCANNING AND SELECTION
echo ============================================================================
echo Scanning for MR/CT/COP/ESP32 devices...
echo This will show you a numbered list of available devices.
echo You can then select which device to update by entering its number.
echo ============================================================================
echo.

if exist "scan_and_select.py" (
    REM Use the advanced scanner with selection - run interactively
    python scan_and_select.py

    REM Check if a selection was made by looking for the output file
    set TARGET_MAC=
    if exist "selected_device.tmp" (
        for /f "delims=" %%a in (selected_device.tmp) do (
            set TARGET_MAC=%%a
        )
        del selected_device.tmp
        echo.
        echo ============================================================================
        echo Device selected successfully: !TARGET_MAC!
        echo ============================================================================
    ) else (
        echo.
        echo ============================================================================
        echo No device was selected. Operation cancelled.
        echo ============================================================================
    )
    
    REM Check if a device was selected
    if "!TARGET_MAC!"=="" (
        echo.
        echo No device selected. Exiting.
        pause
        exit /b 1
    )
    
    echo.
    echo Selected MAC address: !TARGET_MAC!
    echo.
    echo Ready to flash firmware to: !TARGET_MAC!
    echo Firmware: %FIRMWARE_NAME% (Version: %VERSION_INFO%)
    echo.
    set /p confirm="Continue with firmware update? (Y/N): "
    if /i not "!confirm!"=="Y" (
        echo Firmware update cancelled.
        pause
        exit /b 0
    )

    REM Jump directly to firmware transfer since we have the MAC address
    goto start_firmware_transfer

) else (
    REM Fallback to basic scanner
    echo scan_and_select.py not found, using basic scanner...
    python scan_devices.py
    echo.
    set /p TARGET_MAC="Enter MAC address from scan results: "
)
goto validate_mac

:manual_mac
echo.
set /p TARGET_MAC="Enter MAC address (AA:BB:CC:DD:EE:FF): "

:validate_mac
REM Validate MAC address
if "%TARGET_MAC%"=="" (
    echo ERROR: No MAC address provided
    pause
    exit /b 1
)

:start_firmware_transfer
echo.
echo ============================================================================
echo                           STARTING FIRMWARE TRANSFER
echo ============================================================================
echo Target Device: %TARGET_MAC%
echo Firmware: %FIRMWARE_NAME%
echo Version: %VERSION_INFO%
echo Size: %FIRMWARE_SIZE_KB% KB
echo ============================================================================
echo.

REM Execute the Python script
echo Starting firmware transfer...
python "%PYTHON_SCRIPT%" "%FIRMWARE_FILE%" --address "%TARGET_MAC%" --verbose

REM Check exit code
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ============================================================================
    echo                    FIRMWARE FLASH COMPLETED SUCCESSFULLY!
    echo ============================================================================
    echo Device is now running firmware version: %VERSION_INFO%
    echo The device will restart automatically with the updated firmware.
    echo You can now close this window.
) else if %ERRORLEVEL% EQU 2 (
    REM Same version detected - this is not an error, just information
    echo.
    echo ============================================================================
    echo                      FIRMWARE UPDATE NOT NEEDED
    echo ============================================================================
    echo The device already has the same firmware version installed.
    echo No update is required.
) else (
    echo.
    echo ============================================================================
    echo                        FIRMWARE FLASH FAILED!
    echo ============================================================================
    echo Please check the error messages above and try again.
    echo.
    echo Common troubleshooting tips:
    echo   - Make sure device is powered on and advertising
    echo   - Verify MAC address is correct
    echo   - Check device name starts with: MR, CT, COP, or contains ESP32
    echo   - Move closer to the device
    echo   - Ensure no other apps are connected to the device
)

echo.
pause
