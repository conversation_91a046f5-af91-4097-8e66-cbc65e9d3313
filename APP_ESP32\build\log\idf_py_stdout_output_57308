ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x1578
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd04
load:0x403cb700,len:0x2f00
entry 0x403c8918
[0;32mI (27) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (27) boot: compile time Jul 26 2025 18:44:41[0m
[0;32mI (27) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v0.2[0m
[0;32mI (30) boot: efuse block revision: v1.3[0m
[0;32mI (33) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (37) boot.esp32s3: SPI Mode       : DIO[0m
[0;32mI (41) boot.esp32s3: SPI Flash Size : 16MB[0m
[0;32mI (45) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (52) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (58) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (65) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (71) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (78) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (84) boot: End of partition table[0m
[0;32mI (88) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18978h (100728) map[0m
[0;32mI (113) esp_image: segment 1: paddr=000289a0 vaddr=3fc9b900 size=04a94h ( 19092) load[0m
[0;32mI (117) esp_image: segment 2: paddr=0002d43c vaddr=40374000 size=02bdch ( 11228) load[0m
[0;32mI (120) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=623ach (402348) map[0m
[0;32mI (195) esp_image: segment 4: paddr=000923d4 vaddr=40376bdc size=14d00h ( 85248) load[0m
[0;32mI (214) esp_image: segment 5: paddr=000a70dc vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (223) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (223) boot: Disabling RNG early entropy source...[0m
[0;32mI (234) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (234) octal_psram: dev id       : 0x02 (generation 3)[0m
[0;32mI (234) octal_psram: density      : 0x03 (64 Mbit)[0m
[0;32mI (236) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (240) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (245) octal_psram: VCC          : 0x01 (3V)[0m
[0;32mI (249) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (254) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (258) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (263) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (268) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (273) MSPI Timing: PSRAM timing tuning index: 4[0m
[0;32mI (277) esp_psram: Found 8MB PSRAM device[0m
[0;32mI (281) esp_psram: Speed: 80MHz[0m
[0;32mI (284) cpu_start: Multicore app[0m
[0;32mI (712) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (729) cpu_start: Pro cpu start user code[0m
[0;32mI (729) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (729) app_init: Application information:[0m
[0;32mI (729) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (735) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (741) app_init: Compile time:     Jul 26 2025 18:43:34[0m
[0;32mI (746) app_init: ELF file SHA256:  895c240b8...[0m
[0;32mI (750) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (754) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (758) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (762) efuse_init: Chip rev:         v0.2[0m
[0;32mI (766) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (772) heap_init: At 3FCAB6C8 len 0003E048 (248 KiB): RAM[0m
[0;32mI (777) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (783) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (788) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (793) esp_psram: Adding pool of 7929K of PSRAM memory to heap allocator[0m
[0;32mI (800) spi_flash: detected chip: boya[0m
[0;32mI (803) spi_flash: flash io: dio[0m
[0;32mI (806) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (812) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (819) coexist: coex firmware version: e727207[0m
[0;32mI (843) coexist: coexist rom version e7ae62f[0m
[0;32mI (844) main_task: Started on CPU0[0m
[0;32mI (854) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (854) main_task: Calling app_main()[0m
[0;32mI (864) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (864) BLE_INIT: Bluetooth MAC: a0:85:e3:f1:8c:c6[0m
[0;32mI (864) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (904) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(0)[0m
[0;32mI (924) NimBLE_BLE_PRPH: BLE Security initialized - Address verification mode[0m
[0;32mI (924) uart: queue free spaces: 80[0m
[0;32mI (924) FLASH: : 0[0m
[0;32mI (924) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (924) HEAP: Free heap: 8295104[0m
[0;32mI (934) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (934) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (934) NEXUS_HANDLE: : TASK CREATED[0m
[0;32mI (944) NimBLE: Device Address: [0m
[0;32mI (944) NimBLE: a0:85:e3:f1:8c:c6[0m
[0;32mI (954) NimBLE: [0m

[0;32mI (934) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (964) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (964) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (974) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (984) main_task: Returned from app_main()[0m
[0;32mI (100134) UART_READ: : ERROR[0m
0x83348F8
[0;32mI (100134) UART READ: [LEN: ]: 4[0m
