#!/usr/bin/env python3
"""
ESP32 BLE Bonding Test Script
Tests BLE bonding functionality via UART commands
"""

import serial
import struct
import time
import argparse
import sys
from typing import Optional

################################################################################
# CONSTANTS AND CONFIGURATION
################################################################################

# UART Frame Constants (matching ESP32 utility.h)
START_BYTE = 0xA5      # Frame start delimiter
BLE_BOND_ENABLE = 0x14   # Command to enable BLE bonding
BLE_BOND_DISABLE = 0x15  # Command to disable BLE bonding
BLE_BOND_CLEAR = 0x16    # Command to clear all bonding info
BLE_BOND_STATUS = 0x17   # Command to get bonding status
BLE_ACK = 0x01         # Acknowledgment response
BLE_NACK = 0x02        # Negative acknowledgment response

# Default UART settings
DEFAULT_PORT = "COM3"
DEFAULT_BAUDRATE = 2000000  # 2 Mbps as per your configuration

################################################################################
# UTILITY FUNCTIONS
################################################################################

def calculate_crc32(data: bytes) -> int:
    """Calculate CRC32 for the given data."""
    import zlib
    return zlib.crc32(data) & 0xffffffff

def create_uart_frame(command: int, payload: bytes = b'') -> bytes:
    """
    Create a UART frame with the specified command and payload.
    
    Frame format:
    [START_BYTE][COMMAND][LENGTH(4)][PAYLOAD][CRC32(4)]
    """
    # Create header
    header = struct.pack('<BBL', START_BYTE, command, len(payload))
    
    # Calculate CRC32 over header + payload
    frame_data = header + payload
    crc = calculate_crc32(frame_data)
    
    # Complete frame
    frame = frame_data + struct.pack('<L', crc)
    
    return frame

def send_command_and_wait_response(ser: serial.Serial, command: int, payload: bytes = b'', timeout: float = 2.0) -> bool:
    """
    Send a command and wait for ACK/NACK response.
    
    Returns:
        True if ACK received, False if NACK or timeout
    """
    frame = create_uart_frame(command, payload)
    
    print(f"📤 Sending command 0x{command:02X} with {len(payload)} bytes payload")
    print(f"   Frame: {frame.hex().upper()}")
    
    # Send frame
    ser.write(frame)
    ser.flush()
    
    # Wait for response
    start_time = time.time()
    response_buffer = bytearray()
    
    while time.time() - start_time < timeout:
        if ser.in_waiting > 0:
            data = ser.read(ser.in_waiting)
            response_buffer.extend(data)
            
            # Look for complete response frame
            if len(response_buffer) >= 10:  # Minimum frame size
                # Find start byte
                for i in range(len(response_buffer) - 9):
                    if response_buffer[i] == START_BYTE:
                        # Check if we have enough data for a complete frame
                        if i + 10 <= len(response_buffer):
                            response_type = response_buffer[i + 1]
                            if response_type == BLE_ACK:
                                print("✅ Received ACK")
                                return True
                            elif response_type == BLE_NACK:
                                print("❌ Received NACK")
                                return False
        
        time.sleep(0.01)  # Small delay to prevent busy waiting
    
    print("⏰ Timeout waiting for response")
    return False

################################################################################
# BONDING COMMAND FUNCTIONS
################################################################################

def enable_bonding(ser: serial.Serial) -> bool:
    """Enable BLE bonding."""
    print("\n🔗 Enabling BLE bonding...")
    return send_command_and_wait_response(ser, BLE_BOND_ENABLE)

def disable_bonding(ser: serial.Serial) -> bool:
    """Disable BLE bonding."""
    print("\n🚫 Disabling BLE bonding...")
    return send_command_and_wait_response(ser, BLE_BOND_DISABLE)

def clear_bonding(ser: serial.Serial) -> bool:
    """Clear all bonding information."""
    print("\n🧹 Clearing all bonding information...")
    return send_command_and_wait_response(ser, BLE_BOND_CLEAR)

def get_bonding_status(ser: serial.Serial) -> bool:
    """Get current bonding status."""
    print("\n📊 Getting bonding status...")
    return send_command_and_wait_response(ser, BLE_BOND_STATUS)

################################################################################
# INTERACTIVE MODE
################################################################################

def interactive_mode(ser: serial.Serial):
    """Interactive mode for testing bonding commands."""
    print("\n🎮 Interactive Bonding Test Mode")
    print("Commands:")
    print("  1 - Enable bonding")
    print("  2 - Disable bonding")
    print("  3 - Clear all bonding info")
    print("  4 - Get bonding status")
    print("  q - Quit")
    
    while True:
        try:
            choice = input("\nEnter command: ").strip().lower()
            
            if choice == 'q':
                break
            elif choice == '1':
                enable_bonding(ser)
            elif choice == '2':
                disable_bonding(ser)
            elif choice == '3':
                clear_bonding(ser)
            elif choice == '4':
                get_bonding_status(ser)
            else:
                print("❓ Invalid command. Try again.")
                
        except KeyboardInterrupt:
            print("\n👋 Exiting interactive mode...")
            break

################################################################################
# TEST SEQUENCES
################################################################################

def test_bonding_workflow(ser: serial.Serial):
    """Test complete bonding workflow."""
    print("\n🧪 Testing complete bonding workflow...")
    
    tests = [
        ("Get initial status", lambda: get_bonding_status(ser)),
        ("Enable bonding", lambda: enable_bonding(ser)),
        ("Get status after enable", lambda: get_bonding_status(ser)),
        ("Disable bonding", lambda: disable_bonding(ser)),
        ("Get status after disable", lambda: get_bonding_status(ser)),
        ("Clear bonding info", lambda: clear_bonding(ser)),
        ("Get final status", lambda: get_bonding_status(ser)),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        result = test_func()
        results.append((test_name, result))
        time.sleep(0.5)  # Small delay between tests
    
    # Print summary
    print("\n📈 Test Results Summary:")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")

################################################################################
# MAIN FUNCTION
################################################################################

def main():
    parser = argparse.ArgumentParser(description='ESP32 BLE Bonding Test Tool')
    parser.add_argument('--port', '-p', default=DEFAULT_PORT, 
                       help=f'Serial port (default: {DEFAULT_PORT})')
    parser.add_argument('--baudrate', '-b', type=int, default=DEFAULT_BAUDRATE,
                       help=f'Baud rate (default: {DEFAULT_BAUDRATE})')
    parser.add_argument('--command', '-c', choices=['enable', 'disable', 'clear', 'status', 'test', 'interactive'],
                       default='interactive', help='Command to execute')
    
    args = parser.parse_args()
    
    print(f"🔌 Connecting to {args.port} at {args.baudrate} baud...")
    
    try:
        # Open serial connection
        ser = serial.Serial(
            port=args.port,
            baudrate=args.baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
        
        print(f"✅ Connected successfully!")

        # Clear buffers and stabilize connection
        ser.reset_input_buffer()
        ser.reset_output_buffer()
        time.sleep(0.2)

        # Additional settings for high speed reliability
        ser.write_timeout = 1
        ser.inter_byte_timeout = 0.001
        
        # Execute command
        if args.command == 'enable':
            enable_bonding(ser)
        elif args.command == 'disable':
            disable_bonding(ser)
        elif args.command == 'clear':
            clear_bonding(ser)
        elif args.command == 'status':
            get_bonding_status(ser)
        elif args.command == 'test':
            test_bonding_workflow(ser)
        elif args.command == 'interactive':
            interactive_mode(ser)
        
        ser.close()
        print("\n👋 Connection closed.")
        
    except serial.SerialException as e:
        print(f"❌ Serial connection error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
