/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : crc.c
* @version        : 1.0.3
* @brief          : Handle the CRC functionality
* @details        : Handle the CRC functionality
********************************************************************************
* @version 1.0.1                                         				Date : 09/07/2025
* [+] Added debug comments to check the CRC during debug
********************************************************************************
* @version 1.0.2                                         				Date : 18/07/2025
* [-] Removed unused debug command
* [~] Used table-based CRC for faster computation
********************************************************************************
* @version 1.0.3                                         				Date : 23/07/2025
* [+] Added LED control functionality for CRC error indication
********************************************************************************/

#include "crc.h"
#include "esp_log.h"
#include "utility.h"
#include "led_control.h"

/* DEFINES */
#define CRC_MASK                (0xFFFFFFFF)
#define CRC32_POLY_REFLECTED    (0xEDB88320)
#define CRC_SIZE                (256)
#define FINAL_XOR   		(0xFFFFFFFF)

/* VARIABLES */
static uint32_t crc_table[CRC_SIZE];
static int table_initialized = 0;

/* FUNCTION PROTOTYPE */

/**
 * @brief Initializes the CRC-32 computation module or context.
 *
 * This function prepares any required hardware or software resources
 * for CRC-32 checksum calculation. It is typically called once before
 * performing any CRC operations, especially if the CRC engine requires
 * setup or state reset.
 *
 * For pure software implementations, this may be left empty or used to
 * initialize lookup tables or internal variables.
 *
 * @note This implementation uses the standard polynomial (0x04C11DB7),
 *       with input and output reflection, and a final XOR step.
 */
void crc32_init(void) {
    if (table_initialized)
        return;

    for (uint32_t i = 0; i < CRC_SIZE; i++) {
        uint32_t crc = i;
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 1)
                crc = (crc >> 1) ^ CRC32_POLY_REFLECTED;
            else
                crc >>= 1;
        }
        crc_table[i] = crc;
    }
    table_initialized = 1;
}

/**
 * @brief Calculates CRC-32 checksum for the given input data buffer.
 *
 * This function computes the CRC-32 checksum using the standard polynomial
 * (0x04C11DB7) with input reflection, output reflection, and final XOR,
 * commonly used in Ethernet, ZIP, and other protocols.
 *
 * @param data    Pointer to the input data buffer.
 * @param length  Length of the data buffer in bytes.
 *
 * @return        Computed CRC-32 value as a 32-bit unsigned integer.
 */
uint32_t crc32(const uint8_t *data, uint32_t length) {
    uint32_t crc = CRC_MASK;

    for (uint32_t i = 0; i < length; ++i) {
        uint8_t index = (uint8_t)((crc ^ data[i]) & 0xFF);
        crc = (crc >> 8) ^ crc_table[index];
    }

    return crc ^ FINAL_XOR;
}

/**
 * @brief Validates the CRC-32 of a given data buffer against an expected CRC value.
 *
 * This function computes the CRC-32 of the provided data buffer and compares
 * it with the CRC value received (typically included in a packet). It checks
 * data integrity by ensuring the computed CRC matches the provided one.
 *
 * @param data            Pointer to the input data buffer (excluding the CRC itself).
 * @param length          Length of the data buffer in bytes.
 * @param crc_in_packet   The expected CRC-32 value received in the packet.
 *
 * @return                true if the computed CRC matches the provided CRC,
 *                        false otherwise.
 */
bool validate_crc(const uint8_t *data, uint32_t length, uint32_t crc_in_packet)
{
	uint32_t crc_calc = crc32(data, length);
	if (crc_calc == crc_in_packet)
	{
		led_set_error(false, false);  // RED LED: OFF on Valid crc
		return true;
	}
	else
	{
		#ifdef DEBUG_CMD
			ESP_LOGI("LENGTH", "0x%X", (int)length);
			ESP_LOGI("CRC CALCULATED", "0x%X", (int)crc_calc);
			ESP_LOGI("CRC PACKET", "0x%X", (int)crc_in_packet);
			ESP_LOGI("CRC", "FAIL");
		#endif
		led_set_error(true, false);  // RED LED: Blink red on UART errors
		return false;
	}
}
